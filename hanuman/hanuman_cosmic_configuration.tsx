import React, { useState, useEffect } from 'react';
import { <PERSON>tings, Moon, Sun, Star, Compass, Calendar, Clock, Zap, Waves, Triangle, Circle, Hexagon, Play, Pause, RotateCcw, Save, Download, Upload, Eye, EyeOff } from 'lucide-react';

const HanumanCosmicConfiguration = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [activeTab, setActiveTab] = useState('cosmic');
  const [cosmicSettings, setCosmicSettings] = useState({
    seasonalAlignment: 'auto', // auto, spring, summer, autumn, winter
    lunarPhaseSync: true,
    planetaryInfluence: true,
    sacredNumbersMode: true,
    goldenRatioUI: true,
    fibonacciCycles: true,
    cosmicFrequency432: true,
    omFrequency136: true,
    mantrasEnabled: true,
    divineValidation: true
  });

  const [numericalSettings, setNumericalSettings] = useState({
    goldenRatio: 1.618033988749895,
    pi: 3.141592653589793,
    eulerE: 2.718281828459045,
    fibonacci: [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987],
    sacred108: 108,
    sacred21: 21,
    sacred9: 9,
    sacred7: 7,
    cosmicFreq: 432,
    omFreq: 136.1
  });

  const [trimurtiBalance, setTrimurtiBalance] = useState({
    brahma: 33.3, // Création
    vishnu: 33.3, // Conservation
    shiva: 33.4   // Transformation
  });

  const [agentConfiguration, setAgentConfiguration] = useState({
    cortexCentral: { priority: 'high', cosmicSync: true, mantras: true },
    cortexCreative: { priority: 'high', cosmicSync: true, mantras: true },
    cortexLogical: { priority: 'medium', cosmicSync: true, mantras: false },
    cortexAnalytical: { priority: 'medium', cosmicSync: false, mantras: false },
    systemeLimbique: { priority: 'high', cosmicSync: true, mantras: true },
    systemeImmunitaire: { priority: 'critical', cosmicSync: true, mantras: true },
    neuroplasticite: { priority: 'high', cosmicSync: true, mantras: true }
  });

  const [currentSeason, setCurrentSeason] = useState('spring');
  const [currentLunarPhase, setCurrentLunarPhase] = useState('waxing');
  const [currentPlanet, setCurrentPlanet] = useState('jupiter');

  const seasons = [
    { id: 'spring', name: 'Printemps', emoji: '🌸', energy: 'Brahma', description: 'Création et Innovation' },
    { id: 'summer', name: 'Été', emoji: '☀️', energy: 'Vishnu', description: 'Conservation et Croissance' },
    { id: 'autumn', name: 'Automne', emoji: '🍂', energy: 'Shiva', description: 'Transformation et Purification' },
    { id: 'winter', name: 'Hiver', emoji: '❄️', energy: 'Équilibre', description: 'Contemplation et Régénération' }
  ];

  const lunarPhases = [
    { id: 'new', name: 'Nouvelle Lune', emoji: '🌑', energy: 'Création Pure' },
    { id: 'waxing', name: 'Lune Croissante', emoji: '🌒', energy: 'Croissance' },
    { id: 'full', name: 'Pleine Lune', emoji: '🌕', energy: 'Transformation Peak' },
    { id: 'waning', name: 'Lune Décroissante', emoji: '🌘', energy: 'Purification' }
  ];

  const planets = [
    { id: 'sun', name: 'Surya (Soleil)', emoji: '☀️', influence: 'Leadership' },
    { id: 'moon', name: 'Chandra (Lune)', emoji: '🌙', influence: 'Intuition' },
    { id: 'mars', name: 'Mangal (Mars)', emoji: '🔴', influence: 'Action' },
    { id: 'mercury', name: 'Budha (Mercure)', emoji: '☿️', influence: 'Communication' },
    { id: 'jupiter', name: 'Guru (Jupiter)', emoji: '🪐', influence: 'Sagesse' },
    { id: 'venus', name: 'Shukra (Vénus)', emoji: '♀️', influence: 'Beauté' },
    { id: 'saturn', name: 'Shani (Saturne)', emoji: '🪐', influence: 'Discipline' }
  ];

  const updateCosmicSetting = (key, value) => {
    setCosmicSettings(prev => ({ ...prev, [key]: value }));
  };

  const updateTrimurtiBalance = (energy, value) => {
    const newBalance = { ...trimurtiBalance };
    const oldValue = newBalance[energy];
    const diff = value - oldValue;
    
    // Redistribuer la différence sur les autres énergies
    const others = Object.keys(newBalance).filter(k => k !== energy);
    others.forEach(other => {
      newBalance[other] = Math.max(0, newBalance[other] - diff / others.length);
    });
    
    newBalance[energy] = value;
    
    // Normaliser pour que la somme soit 100
    const total = Object.values(newBalance).reduce((sum, val) => sum + val, 0);
    Object.keys(newBalance).forEach(key => {
      newBalance[key] = (newBalance[key] / total) * 100;
    });
    
    setTrimurtiBalance(newBalance);
  };

  const exportConfiguration = () => {
    const config = {
      cosmicSettings,
      numericalSettings,
      trimurtiBalance,
      agentConfiguration,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hanuman-cosmic-config-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetToDefaults = () => {
    setCosmicSettings({
      seasonalAlignment: 'auto',
      lunarPhaseSync: true,
      planetaryInfluence: true,
      sacredNumbersMode: true,
      goldenRatioUI: true,
      fibonacciCycles: true,
      cosmicFrequency432: true,
      omFrequency136: true,
      mantrasEnabled: true,
      divineValidation: true
    });
    
    setTrimurtiBalance({
      brahma: 33.3,
      vishnu: 33.3,
      shiva: 33.4
    });
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
              <Settings className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Configuration Cosmique Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Alignement Astral • Paramètres Sacrés • Harmonisation Divine
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={exportConfiguration}
              className="p-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors"
            >
              <Download size={18} />
            </button>
            <button
              onClick={resetToDefaults}
              className="p-3 bg-orange-500 text-white rounded-xl hover:bg-orange-600 transition-colors"
            >
              <RotateCcw size={18} />
            </button>
            <button
              onClick={() => setDarkMode(!darkMode)}
              className={`p-3 rounded-xl transition-colors ${
                darkMode 
                  ? 'bg-gray-800 hover:bg-gray-700 text-yellow-400' 
                  : 'bg-white hover:bg-gray-100 text-gray-600'
              }`}
            >
              {darkMode ? <Sun size={18} /> : <Moon size={18} />}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex space-x-2 mb-6">
          {[
            { id: 'cosmic', label: 'Alignement Cosmique', icon: Compass },
            { id: 'trimurti', label: 'Équilibre Trimurti', icon: Triangle },
            { id: 'numbers', label: 'Nombres Sacrés', icon: Hexagon },
            { id: 'agents', label: 'Configuration Agents', icon: Settings }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? darkMode ? 'bg-purple-600 text-white' : 'bg-purple-500 text-white'
                    : darkMode ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Contenu */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {activeTab === 'cosmic' && (
            <>
              {/* Alignement Saisonnier */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Alignement Saisonnier
                </h3>
                <div className="space-y-3">
                  {seasons.map(season => (
                    <div
                      key={season.id}
                      onClick={() => setCurrentSeason(season.id)}
                      className={`p-3 rounded-lg cursor-pointer transition-all ${
                        currentSeason === season.id
                          ? 'bg-purple-100 dark:bg-purple-900 border-2 border-purple-500'
                          : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{season.emoji}</span>
                          <div>
                            <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                              {season.name}
                            </h4>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {season.energy} • {season.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Phases Lunaires */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Phases Lunaires
                </h3>
                <div className="space-y-3">
                  {lunarPhases.map(phase => (
                    <div
                      key={phase.id}
                      onClick={() => setCurrentLunarPhase(phase.id)}
                      className={`p-3 rounded-lg cursor-pointer transition-all ${
                        currentLunarPhase === phase.id
                          ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-500'
                          : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{phase.emoji}</span>
                        <div>
                          <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {phase.name}
                          </h4>
                          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {phase.energy}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Influences Planétaires */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Influences Planétaires
                </h3>
                <div className="space-y-3">
                  {planets.map(planet => (
                    <div
                      key={planet.id}
                      onClick={() => setCurrentPlanet(planet.id)}
                      className={`p-3 rounded-lg cursor-pointer transition-all ${
                        currentPlanet === planet.id
                          ? 'bg-yellow-100 dark:bg-yellow-900 border-2 border-yellow-500'
                          : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-xl">{planet.emoji}</span>
                        <div>
                          <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {planet.name}
                          </h4>
                          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {planet.influence}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {activeTab === 'trimurti' && (
            <>
              {/* Équilibre Trimurti */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Équilibre des Énergies Trimurti
                </h3>
                <div className="space-y-6">
                  {Object.entries(trimurtiBalance).map(([energy, value]) => {
                    const colors = {
                      brahma: 'yellow',
                      vishnu: 'blue', 
                      shiva: 'red'
                    };
                    const emojis = {
                      brahma: '🌅',
                      vishnu: '🌊',
                      shiva: '🔥'
                    };
                    const descriptions = {
                      brahma: 'Création • Innovation • Nouveaux Projets',
                      vishnu: 'Conservation • Stabilité • Croissance',
                      shiva: 'Transformation • Purification • Optimisation'
                    };
                    
                    return (
                      <div key={energy}>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{emojis[energy]}</span>
                            <div>
                              <h4 className={`font-medium capitalize ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                {energy}
                              </h4>
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                {descriptions[energy]}
                              </p>
                            </div>
                          </div>
                          <span className={`text-lg font-bold text-${colors[energy]}-500`}>
                            {value.toFixed(1)}%
                          </span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={value}
                          onChange={(e) => updateTrimurtiBalance(energy, parseFloat(e.target.value))}
                          className={`w-full h-3 rounded-lg appearance-none cursor-pointer bg-gray-300 dark:bg-gray-600`}
                          style={{
                            background: `linear-gradient(to right, var(--tw-${colors[energy]}-500) 0%, var(--tw-${colors[energy]}-500) ${value}%, var(--tw-gray-300) ${value}%, var(--tw-gray-300) 100%)`
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Visualisation Trimurti */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Mandala Trimurti
                </h3>
                <div className="text-center">
                  <div className="relative w-48 h-48 mx-auto">
                    <div className="absolute inset-0 rounded-full border-8 border-yellow-400" 
                         style={{ clipPath: `polygon(50% 50%, 50% 0%, ${50 + trimurtiBalance.brahma/2}% 0%, 50% 50%)` }}>
                    </div>
                    <div className="absolute inset-0 rounded-full border-8 border-blue-400"
                         style={{ clipPath: `polygon(50% 50%, ${50 + trimurtiBalance.brahma/2}% 0%, 100% 50%, 50% 50%)` }}>
                    </div>
                    <div className="absolute inset-0 rounded-full border-8 border-red-400"
                         style={{ clipPath: `polygon(50% 50%, 100% 50%, 50% 100%, 50% 50%)` }}>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-3xl">🕉️</span>
                    </div>
                  </div>
                  <div className="mt-4 text-sm text-gray-500">
                    Équilibre Cosmique Actuel
                  </div>
                </div>
              </div>
            </>
          )}

          {activeTab === 'numbers' && (
            <>
              {/* Nombres Sacrés */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Constantes Sacrées
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(numericalSettings).slice(0, 10).map(([key, value]) => (
                    <div key={key} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <div className="flex items-center justify-between">
                        <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} capitalize`}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                        <span className={`font-mono text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {Array.isArray(value) ? `[${value.slice(0, 3).join(', ')}...]` : value.toFixed ? value.toFixed(6) : value}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Paramètres Cosmiques */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Paramètres Cosmiques
                </h3>
                <div className="space-y-4">
                  {Object.entries(cosmicSettings).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'} capitalize`}>
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <button
                        onClick={() => updateCosmicSetting(key, !value)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          value ? 'bg-purple-600' : 'bg-gray-300 dark:bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            value ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {activeTab === 'agents' && (
            <>
              {/* Configuration Agents */}
              <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-3`}>
                <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Configuration des Agents
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(agentConfiguration).map(([agentKey, config]) => (
                    <div key={agentKey} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <h4 className={`font-medium mb-3 ${darkMode ? 'text-white' : 'text-gray-900'} capitalize`}>
                        {agentKey.replace(/([A-Z])/g, ' $1').trim()}
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Priorité
                          </label>
                          <select
                            value={config.priority}
                            onChange={(e) => setAgentConfiguration(prev => ({
                              ...prev,
                              [agentKey]: { ...config, priority: e.target.value }
                            }))}
                            className={`w-full mt-1 p-2 rounded text-sm ${
                              darkMode ? 'bg-gray-600 text-white' : 'bg-white text-gray-900'
                            }`}
                          >
                            <option value="low">Basse</option>
                            <option value="medium">Moyenne</option>
                            <option value="high">Haute</option>
                            <option value="critical">Critique</option>
                          </select>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Sync Cosmique
                          </span>
                          <button
                            onClick={() => setAgentConfiguration(prev => ({
                              ...prev,
                              [agentKey]: { ...config, cosmicSync: !config.cosmicSync }
                            }))}
                            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                              config.cosmicSync ? 'bg-purple-600' : 'bg-gray-300 dark:bg-gray-600'
                            }`}
                          >
                            <span
                              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                                config.cosmicSync ? 'translate-x-5' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Mantras
                          </span>
                          <button
                            onClick={() => setAgentConfiguration(prev => ({
                              ...prev,
                              [agentKey]: { ...config, mantras: !config.mantras }
                            }))}
                            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                              config.mantras ? 'bg-purple-600' : 'bg-gray-300 dark:bg-gray-600'
                            }`}
                          >
                            <span
                              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                                config.mantras ? 'translate-x-5' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HanumanCosmicConfiguration;
