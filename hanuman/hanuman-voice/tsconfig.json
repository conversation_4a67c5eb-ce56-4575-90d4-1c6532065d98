{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@core/*": ["core/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"], "@api/*": ["api/*"], "@dashboard/*": ["dashboard/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.spec.ts", "**/*.test.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}