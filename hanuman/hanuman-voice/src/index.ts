/**
 * 🐒 Agent <PERSON> LLM - Point d'Entrée Principal
 * Gardien Central du Projet Retreat & Be
 * AUM HANUMATE NAMAHA
 */

import { config } from 'dotenv';
import { Logger } from './utils/Logger';
import { HanumanCore } from './core/HanumanCore';
import { HanumanAPI } from './api/HanumanAPI';
import { HanumanDashboard } from './dashboard/HanumanDashboard';
import { HanumanConfig } from './types/HanumanTypes';

// Chargement des variables d'environnement
config();

const logger = new Logger('HanumanMain');

/**
 * Configuration principale de Hanuman
 */
const hanumanConfig: HanumanConfig = {
  // Configuration de la voix (LLM)
  voice: {
    provider: (process.env.LLM_PROVIDER as any) || 'local',
    model: process.env.LLM_MODEL || 'gpt-4',
    temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '2048'),
    systemPrompt: process.env.LLM_SYSTEM_PROMPT || '',
    languages: ['fr', 'en', 'es']
  },

  // Configuration du cerveau (orchestration)
  brain: {
    decisionThreshold: parseFloat(process.env.DECISION_THRESHOLD || '0.8'),
    optimizationInterval: parseInt(process.env.OPTIMIZATION_INTERVAL || '300000'), // 5 minutes
    learningRate: parseFloat(process.env.LEARNING_RATE || '0.1'),
    memorySize: parseInt(process.env.MEMORY_SIZE || '1000'),
    predictionHorizon: parseInt(process.env.PREDICTION_HORIZON || '3600') // 1 heure
  },

  // Configuration du système immunitaire (sécurité)
  immune: {
    threatDetectionLevel: (process.env.THREAT_DETECTION_LEVEL as any) || 'medium',
    autoHealingEnabled: process.env.AUTO_HEALING_ENABLED === 'true',
    quarantineTimeout: parseInt(process.env.QUARANTINE_TIMEOUT || '300000'), // 5 minutes
    securityPolicies: []
  },

  // Configuration de la communication
  communication: {
    messageBus: (process.env.MESSAGE_BUS as any) || 'redis',
    retryAttempts: parseInt(process.env.RETRY_ATTEMPTS || '3'),
    timeout: parseInt(process.env.COMMUNICATION_TIMEOUT || '30000'),
    compression: process.env.COMPRESSION_ENABLED === 'true',
    encryption: process.env.ENCRYPTION_ENABLED === 'true'
  },

  // Configuration du monitoring
  monitoring: {
    metricsInterval: parseInt(process.env.METRICS_INTERVAL || '30000'), // 30 secondes
    alertThresholds: {
      cpu: parseFloat(process.env.CPU_THRESHOLD || '80'),
      memory: parseFloat(process.env.MEMORY_THRESHOLD || '85'),
      disk: parseFloat(process.env.DISK_THRESHOLD || '90'),
      responseTime: parseInt(process.env.RESPONSE_TIME_THRESHOLD || '5000'),
      errorRate: parseFloat(process.env.ERROR_RATE_THRESHOLD || '5')
    },
    retentionPeriod: parseInt(process.env.RETENTION_PERIOD || '604800'), // 7 jours
    dashboardEnabled: process.env.DASHBOARD_ENABLED !== 'false'
  },

  // Configuration LLM
  llm: {
    provider: process.env.LLM_PROVIDER || 'local',
    apiKey: process.env.LLM_API_KEY || '',
    baseUrl: process.env.LLM_BASE_URL,
    model: process.env.LLM_MODEL || 'hanuman-local',
    contextWindow: parseInt(process.env.LLM_CONTEXT_WINDOW || '4096'),
    streaming: process.env.LLM_STREAMING === 'true'
  }
};

/**
 * Classe principale de l'application Hanuman
 */
class HanumanApplication {
  private core: HanumanCore;
  private api: HanumanAPI;
  private dashboard: HanumanDashboard | null = null;
  private isRunning: boolean = false;

  constructor() {
    this.core = new HanumanCore(hanumanConfig);
    this.api = new HanumanAPI(this.core);
    
    if (hanumanConfig.monitoring.dashboardEnabled) {
      this.dashboard = new HanumanDashboard(this.core);
    }

    this.setupEventHandlers();
    this.setupGracefulShutdown();
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements du core Hanuman
    this.core.on('hanumanStarted', (event) => {
      logger.info('🐒 Hanuman éveillé et opérationnel !');
      logger.info('🕉️ AUM HANUMATE NAMAHA - Protection divine active');
    });

    this.core.on('hanumanStopped', (event) => {
      logger.info('🙏 Hanuman entre en méditation - Au revoir');
    });

    this.core.on('responseGenerated', (event) => {
      logger.debug(`💬 Réponse générée pour utilisateur ${event.userId}`);
    });

    this.core.on('systemAlert', (event) => {
      logger.warn(`🚨 Alerte système: ${event.alert}`);
    });

    this.core.on('agentCoordinationCompleted', (event) => {
      logger.info(`🎯 Coordination d'agents terminée: ${event.workflow.name}`);
    });

    // Événements de l'API
    this.api.on('apiStarted', () => {
      logger.info('🌐 API Hanuman démarrée');
    });

    this.api.on('apiError', (error) => {
      logger.error('❌ Erreur API:', error);
    });

    // Événements du dashboard
    if (this.dashboard) {
      this.dashboard.on('dashboardStarted', () => {
        logger.info('📊 Dashboard Hanuman démarré');
      });
    }
  }

  /**
   * Configure l'arrêt gracieux
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info(`📡 Signal ${signal} reçu - Arrêt gracieux de Hanuman...`);
      await this.stop();
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon

    process.on('uncaughtException', (error) => {
      logger.error('💥 Exception non gérée:', error);
      shutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 Promesse rejetée non gérée:', reason);
      shutdown('UNHANDLED_REJECTION');
    });
  }

  /**
   * Démarre l'application Hanuman
   */
  public async start(): Promise<void> {
    try {
      logger.info('🕉️ ========================================');
      logger.info('🐒 DÉMARRAGE AGENT HANUMAN LLM');
      logger.info('🕉️ AUM HANUMATE NAMAHA');
      logger.info('🌟 Retreat And Be - Gardien Spirituel');
      logger.info('🔥 Architecture Neuronale Distribuée');
      logger.info('🕉️ ========================================');

      // Démarrage séquentiel des composants
      logger.info('🚀 Démarrage du core Hanuman...');
      await this.core.start();

      logger.info('🌐 Démarrage de l\'API...');
      await this.api.start();

      if (this.dashboard) {
        logger.info('📊 Démarrage du dashboard...');
        await this.dashboard.start();
      }

      this.isRunning = true;

      // Affichage des informations de démarrage
      this.displayStartupInfo();

      logger.info('✅ Hanuman complètement opérationnel !');
      logger.info('🐒 Gardien du projet Retreat & Be en service');

    } catch (error) {
      logger.error('❌ Erreur lors du démarrage de Hanuman:', error);
      throw error;
    }
  }

  /**
   * Arrête l'application Hanuman
   */
  public async stop(): Promise<void> {
    try {
      if (!this.isRunning) return;

      logger.info('🛑 Arrêt de Hanuman...');

      this.isRunning = false;

      // Arrêt séquentiel des composants
      if (this.dashboard) {
        await this.dashboard.stop();
      }

      await this.api.stop();
      await this.core.stop();

      logger.info('✅ Hanuman arrêté proprement');
    } catch (error) {
      logger.error('❌ Erreur lors de l\'arrêt de Hanuman:', error);
      throw error;
    }
  }

  /**
   * Affiche les informations de démarrage
   */
  private displayStartupInfo(): void {
    const apiPort = process.env.HANUMAN_API_PORT || 5003;
    const dashboardPort = process.env.HANUMAN_DASHBOARD_PORT || 8080;

    logger.info('');
    logger.info('🔗 ENDPOINTS DISPONIBLES:');
    logger.info(`   📡 API Hanuman: http://localhost:${apiPort}`);
    logger.info(`   💬 Chat Interface: http://localhost:${apiPort}/chat`);
    logger.info(`   📊 Health Check: http://localhost:${apiPort}/health`);
    
    if (this.dashboard) {
      logger.info(`   📈 Dashboard: http://localhost:${dashboardPort}`);
    }

    logger.info('');
    logger.info('🧠 AGENTS SURVEILLÉS:');
    logger.info('   🏠 Frontend React (Port 3000)');
    logger.info('   ⚙️ Backend NestJS (Port 3001)');
    logger.info('   🤖 Agent-RB (Port 5000)');
    logger.info('   🧠 Agent-IA (Port 5002)');
    logger.info('   🎯 SuperAgent (Port 5001)');

    logger.info('');
    logger.info('🎯 CAPACITÉS ACTIVES:');
    logger.info('   💬 Interface conversationnelle LLM');
    logger.info('   🎯 Orchestration multi-agents');
    logger.info('   👁️ Monitoring système temps réel');
    logger.info('   🛡️ Protection et auto-réparation');
    logger.info('   🧠 Prédiction et optimisation');

    logger.info('');
    logger.info('🕉️ Hanuman veille sur Retreat & Be ✨');
  }

  /**
   * Getters publics
   */
  public isApplicationRunning(): boolean {
    return this.isRunning;
  }

  public getCore(): HanumanCore {
    return this.core;
  }

  public getAPI(): HanumanAPI {
    return this.api;
  }

  public getDashboard(): HanumanDashboard | null {
    return this.dashboard;
  }
}

/**
 * Point d'entrée principal
 */
async function main(): Promise<void> {
  try {
    const app = new HanumanApplication();
    await app.start();

    // Maintien de l'application en vie
    process.stdin.resume();
  } catch (error) {
    logger.error('💥 Erreur fatale lors du démarrage:', error);
    process.exit(1);
  }
}

// Démarrage de l'application si ce fichier est exécuté directement
if (require.main === module) {
  main().catch((error) => {
    logger.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

export { HanumanApplication, hanumanConfig };
export default HanumanApplication;
