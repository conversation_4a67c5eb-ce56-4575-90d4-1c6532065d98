/**
 * 🐒 Types TypeScript pour l'Agent Hanuman LLM
 * Définitions de types pour l'architecture distribuée
 */

// ===== TYPES DE BASE =====

export interface HanumanConfig {
  voice: VoiceConfig;
  brain: BrainConfig;
  immune: ImmuneConfig;
  communication: CommunicationConfig;
  monitoring: MonitoringConfig;
  llm: LLMConfig;
}

export interface VoiceConfig {
  provider: 'openai' | 'anthropic' | 'local';
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  languages: string[];
}

export interface BrainConfig {
  decisionThreshold: number;
  optimizationInterval: number;
  learningRate: number;
  memorySize: number;
  predictionHorizon: number;
}

export interface ImmuneConfig {
  threatDetectionLevel: 'low' | 'medium' | 'high' | 'paranoid';
  autoHealingEnabled: boolean;
  quarantineTimeout: number;
  securityPolicies: SecurityPolicy[];
}

export interface CommunicationConfig {
  messageBus: 'kafka' | 'redis' | 'nats';
  retryAttempts: number;
  timeout: number;
  compression: boolean;
  encryption: boolean;
}

export interface MonitoringConfig {
  metricsInterval: number;
  alertThresholds: AlertThresholds;
  retentionPeriod: number;
  dashboardEnabled: boolean;
}

export interface LLMConfig {
  provider: string;
  apiKey: string;
  baseUrl?: string;
  model: string;
  contextWindow: number;
  streaming: boolean;
}

// ===== TYPES SYSTÈME =====

export interface SystemStatus {
  health: 'healthy' | 'degraded' | 'critical' | 'unknown';
  load: number;
  alerts: SystemAlert[];
  performance: PerformanceMetrics;
  uptime: number;
  version: string;
}

export interface SystemContext {
  timestamp: Date;
  systemHealth: string;
  activeAgents: AgentInfo[];
  currentLoad: number;
  alerts: SystemAlert[];
  securityLevel?: SecurityLevel;
  performance?: PerformanceMetrics;
  userContext?: UserContext;
}

export interface PerformanceMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  responseTime: number;
  throughput: number;
  errorRate: number;
}

export interface SystemAlert {
  id: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  source: string;
  resolved: boolean;
  actions?: string[];
}

export interface AlertThresholds {
  cpu: number;
  memory: number;
  disk: number;
  responseTime: number;
  errorRate: number;
}

// ===== TYPES AGENTS =====

export interface AgentInfo {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  health: number;
  load: number;
  capabilities: string[];
  lastSeen: Date;
  endpoint: string;
}

export type AgentType = 
  | 'retreat-planner'
  | 'partner-matcher'
  | 'client-assistant'
  | 'content-moderator'
  | 'analytics-engine'
  | 'security-guardian'
  | 'performance-optimizer'
  | 'coordinator'
  | 'supervisor';

export type AgentStatus = 'active' | 'idle' | 'busy' | 'offline' | 'error';

export interface AgentRequest {
  id: string;
  targetAgent: string;
  action: string;
  payload: any;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timeout: number;
  retries: number;
  callback?: string;
}

export interface AgentResponse {
  requestId: string;
  agentId: string;
  status: 'success' | 'error' | 'timeout';
  data?: any;
  error?: string;
  timestamp: Date;
  processingTime: number;
}

export interface AgentWorkflow {
  id: string;
  name: string;
  steps: WorkflowStep[];
  dependencies: WorkflowDependency[];
  timeout: number;
  retryPolicy: RetryPolicy;
  status: WorkflowStatus;
}

export interface WorkflowStep {
  id: string;
  agentId: string;
  action: string;
  input: any;
  output?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
}

export interface WorkflowDependency {
  stepId: string;
  dependsOn: string[];
  condition?: string;
}

export type WorkflowStatus = 'created' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: 'linear' | 'exponential';
  baseDelay: number;
  maxDelay: number;
}

// ===== TYPES SÉCURITÉ =====

export interface SecurityPolicy {
  id: string;
  name: string;
  rules: SecurityRule[];
  enabled: boolean;
  severity: SecuritySeverity;
}

export interface SecurityRule {
  condition: string;
  action: SecurityAction;
  parameters: Record<string, any>;
}

export type SecurityAction = 
  | 'allow'
  | 'deny'
  | 'quarantine'
  | 'alert'
  | 'log'
  | 'rate-limit';

export type SecuritySeverity = 'low' | 'medium' | 'high' | 'critical';

export type SecurityLevel = 'normal' | 'elevated' | 'high' | 'critical';

export interface ThreatAnalysis {
  id: string;
  type: ThreatType;
  severity: SecuritySeverity;
  confidence: number;
  source: string;
  target: string;
  description: string;
  indicators: string[];
  recommendations: string[];
  timestamp: Date;
}

export type ThreatType = 
  | 'malware'
  | 'intrusion'
  | 'ddos'
  | 'data-breach'
  | 'privilege-escalation'
  | 'anomaly'
  | 'policy-violation';

export interface HealingAction {
  id: string;
  type: HealingType;
  target: string;
  description: string;
  status: 'planned' | 'executing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  result?: string;
}

export type HealingType = 
  | 'restart-service'
  | 'scale-up'
  | 'scale-down'
  | 'clear-cache'
  | 'rotate-keys'
  | 'update-config'
  | 'isolate-component';

// ===== TYPES COMMUNICATION =====

export interface HanumanEvent {
  id: string;
  type: string;
  source: string;
  data: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface UserMessage {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
  context?: UserContext;
  metadata?: Record<string, any>;
}

export interface UserContext {
  userId: string;
  sessionId: string;
  preferences: UserPreferences;
  history: ConversationHistory[];
  permissions: string[];
  location?: GeoLocation;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
  verbosity: 'minimal' | 'normal' | 'detailed';
}

export interface ConversationHistory {
  timestamp: Date;
  userMessage: string;
  hanumanResponse: string;
  context: any;
  satisfaction?: number;
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  country: string;
  city: string;
  timezone: string;
}

// ===== TYPES ANALYSE =====

export interface MessageAnalysis {
  intent: MessageIntent;
  entities: ExtractedEntity[];
  sentiment: SentimentAnalysis;
  complexity: number;
  requiresAgentAction: boolean;
  agentRequests: AgentRequest[];
  confidence: number;
  language: string;
}

export interface MessageIntent {
  primary: string;
  secondary?: string[];
  confidence: number;
  category: IntentCategory;
}

export type IntentCategory = 
  | 'information'
  | 'booking'
  | 'support'
  | 'complaint'
  | 'compliment'
  | 'system'
  | 'emergency';

export interface ExtractedEntity {
  type: EntityType;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

export type EntityType = 
  | 'location'
  | 'date'
  | 'person'
  | 'retreat-type'
  | 'price'
  | 'duration'
  | 'skill-level';

export interface SentimentAnalysis {
  polarity: number; // -1 to 1
  subjectivity: number; // 0 to 1
  emotion: EmotionType;
  confidence: number;
}

export type EmotionType = 
  | 'joy'
  | 'sadness'
  | 'anger'
  | 'fear'
  | 'surprise'
  | 'disgust'
  | 'neutral';

// ===== TYPES PRÉDICTION =====

export interface PredictionResult {
  id: string;
  type: PredictionType;
  horizon: number; // en minutes
  confidence: number;
  prediction: any;
  recommendations: string[];
  timestamp: Date;
}

export type PredictionType = 
  | 'load-forecast'
  | 'failure-prediction'
  | 'user-behavior'
  | 'resource-needs'
  | 'security-risk';

// ===== TYPES RÉPONSE =====

export interface HanumanResponse {
  id: string;
  content: string;
  type: ResponseType;
  agentsInvolved: string[];
  confidence: number;
  processingTime: number;
  metadata: ResponseMetadata;
  timestamp: Date;
}

export type ResponseType = 
  | 'direct-answer'
  | 'agent-coordination'
  | 'system-status'
  | 'error-handling'
  | 'learning-response';

export interface ResponseMetadata {
  tokensUsed: number;
  modelUsed: string;
  reasoning: string[];
  sources: string[];
  actions: string[];
}

// ===== EXPORTS =====

export default {
  HanumanConfig,
  SystemStatus,
  SystemContext,
  AgentInfo,
  AgentRequest,
  AgentResponse,
  HanumanEvent,
  UserMessage,
  MessageAnalysis,
  HanumanResponse
};
