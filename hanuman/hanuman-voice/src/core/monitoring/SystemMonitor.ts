/**
 * 👁️ System Monitor - Surveillance de l'Écosystème Retreat & Be
 * Surveille la santé, performance et sécurité de tous les microservices
 */

import { EventEmitter } from 'events';
import { Logger } from '../../utils/Logger';
import { MetricsCollector } from './MetricsCollector';
import { Health<PERSON>hecker } from './HealthChecker';
import { AlertManager } from './AlertManager';
import { 
  MonitoringConfig,
  SystemStatus,
  PerformanceMetrics,
  SystemAlert,
  HanumanEvent,
  AgentInfo
} from '../../types/HanumanTypes';

export class SystemMonitor extends EventEmitter {
  private logger: Logger;
  private config: MonitoringConfig;
  private metricsCollector: MetricsCollector;
  private healthChecker: HealthChecker;
  private alertManager: AlertManager;
  private isActive: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private currentStatus: SystemStatus;

  // Services à surveiller dans l'écosystème Retreat & Be
  private readonly MONITORED_SERVICES = {
    'frontend-react': {
      url: 'http://frontend-service:3000/health',
      type: 'frontend',
      critical: true
    },
    'backend-nestjs': {
      url: 'http://backend-service:3001/health',
      type: 'backend',
      critical: true
    },
    'agent-rb': {
      url: 'http://agent-rb-service:5000/health',
      type: 'agent',
      critical: true
    },
    'agent-ia': {
      url: 'http://agent-ia-service:5002/health',
      type: 'agent',
      critical: true
    },
    'superagent': {
      url: 'http://superagent-service:5001/health',
      type: 'agent',
      critical: true
    },
    'database-postgres': {
      url: 'http://postgres-service:5432',
      type: 'database',
      critical: true
    },
    'cache-redis': {
      url: 'http://redis-service:6379',
      type: 'cache',
      critical: false
    },
    'message-bus': {
      url: 'http://kafka-service:9092',
      type: 'messaging',
      critical: false
    }
  };

  constructor(config: MonitoringConfig) {
    super();
    this.config = config;
    this.logger = new Logger('SystemMonitor');
    
    this.initializeComponents();
    this.initializeStatus();
    
    this.logger.info('👁️ System Monitor initialisé');
  }

  /**
   * Initialise les composants de monitoring
   */
  private initializeComponents(): void {
    try {
      this.metricsCollector = new MetricsCollector(this.config);
      this.healthChecker = new HealthChecker(this.config);
      this.alertManager = new AlertManager(this.config);
      
      this.setupEventHandlers();
      this.logger.info('✅ Composants de monitoring initialisés');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation des composants:', error);
      throw error;
    }
  }

  /**
   * Initialise le statut système
   */
  private initializeStatus(): void {
    this.currentStatus = {
      health: 'unknown',
      load: 0,
      alerts: [],
      performance: {
        cpu: 0,
        memory: 0,
        disk: 0,
        network: 0,
        responseTime: 0,
        throughput: 0,
        errorRate: 0
      },
      uptime: 0,
      version: '1.0.0'
    };
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements du collecteur de métriques
    this.metricsCollector.on('metricsCollected', this.handleMetricsUpdate.bind(this));
    this.metricsCollector.on('anomalyDetected', this.handleAnomalyDetected.bind(this));

    // Événements du vérificateur de santé
    this.healthChecker.on('healthUpdate', this.handleHealthUpdate.bind(this));
    this.healthChecker.on('serviceDown', this.handleServiceDown.bind(this));

    // Événements du gestionnaire d'alertes
    this.alertManager.on('alertTriggered', this.handleAlertTriggered.bind(this));
    this.alertManager.on('alertResolved', this.handleAlertResolved.bind(this));

    this.logger.info('🔗 Gestionnaires d\'événements configurés');
  }

  /**
   * Démarre le monitoring
   */
  public async start(): Promise<void> {
    try {
      this.logger.info('🚀 Démarrage du monitoring système...');

      await this.metricsCollector.start();
      await this.healthChecker.start();
      await this.alertManager.start();

      // Démarrage du monitoring périodique
      this.startPeriodicMonitoring();
      
      this.isActive = true;
      this.logger.info('✅ System Monitor démarré');
    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage du monitoring:', error);
      throw error;
    }
  }

  /**
   * Arrête le monitoring
   */
  public async stop(): Promise<void> {
    try {
      this.isActive = false;
      
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }

      await this.alertManager.stop();
      await this.healthChecker.stop();
      await this.metricsCollector.stop();
      
      this.logger.info('🛑 System Monitor arrêté');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'arrêt du monitoring:', error);
      throw error;
    }
  }

  /**
   * Démarre le monitoring périodique
   */
  private startPeriodicMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performSystemCheck();
      } catch (error) {
        this.logger.error('❌ Erreur lors du check système périodique:', error);
      }
    }, this.config.metricsInterval);

    this.logger.info(`⏰ Monitoring périodique démarré (${this.config.metricsInterval}ms)`);
  }

  /**
   * Effectue une vérification complète du système
   */
  private async performSystemCheck(): Promise<void> {
    try {
      // Collecte des métriques
      const metrics = await this.metricsCollector.collectAllMetrics();
      
      // Vérification de santé des services
      const healthResults = await this.healthChecker.checkAllServices(
        Object.entries(this.MONITORED_SERVICES)
      );

      // Mise à jour du statut système
      await this.updateSystemStatus(metrics, healthResults);

      // Détection d'anomalies
      await this.detectAnomalies(metrics, healthResults);

      this.emit('performanceUpdate', {
        id: `perf_${Date.now()}`,
        type: 'performanceUpdate',
        source: 'SystemMonitor',
        data: { metrics: this.currentStatus.performance },
        timestamp: new Date(),
        priority: 'low'
      } as HanumanEvent);

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification système:', error);
    }
  }

  /**
   * Met à jour le statut système
   */
  private async updateSystemStatus(
    metrics: PerformanceMetrics, 
    healthResults: Map<string, boolean>
  ): Promise<void> {
    // Calcul de la santé globale
    const totalServices = Object.keys(this.MONITORED_SERVICES).length;
    const healthyServices = Array.from(healthResults.values()).filter(h => h).length;
    const healthPercentage = (healthyServices / totalServices) * 100;

    let overallHealth: 'healthy' | 'degraded' | 'critical' | 'unknown';
    if (healthPercentage >= 95) {
      overallHealth = 'healthy';
    } else if (healthPercentage >= 80) {
      overallHealth = 'degraded';
    } else {
      overallHealth = 'critical';
    }

    // Calcul de la charge globale
    const globalLoad = Math.max(metrics.cpu, metrics.memory, metrics.network);

    // Mise à jour du statut
    this.currentStatus = {
      ...this.currentStatus,
      health: overallHealth,
      load: globalLoad,
      performance: metrics,
      uptime: this.calculateUptime()
    };

    this.logger.debug(`📊 Statut système mis à jour: ${overallHealth} (${healthPercentage.toFixed(1)}%)`);
  }

  /**
   * Détecte les anomalies dans le système
   */
  private async detectAnomalies(
    metrics: PerformanceMetrics, 
    healthResults: Map<string, boolean>
  ): Promise<void> {
    const anomalies: string[] = [];

    // Vérification des seuils de performance
    if (metrics.cpu > this.config.alertThresholds.cpu) {
      anomalies.push(`CPU élevé: ${metrics.cpu.toFixed(1)}%`);
    }

    if (metrics.memory > this.config.alertThresholds.memory) {
      anomalies.push(`Mémoire élevée: ${metrics.memory.toFixed(1)}%`);
    }

    if (metrics.responseTime > this.config.alertThresholds.responseTime) {
      anomalies.push(`Temps de réponse élevé: ${metrics.responseTime}ms`);
    }

    if (metrics.errorRate > this.config.alertThresholds.errorRate) {
      anomalies.push(`Taux d'erreur élevé: ${metrics.errorRate.toFixed(2)}%`);
    }

    // Vérification des services critiques
    for (const [serviceName, serviceConfig] of Object.entries(this.MONITORED_SERVICES)) {
      if (serviceConfig.critical && !healthResults.get(serviceName)) {
        anomalies.push(`Service critique indisponible: ${serviceName}`);
      }
    }

    // Émission des anomalies détectées
    if (anomalies.length > 0) {
      this.emit('anomalyDetected', {
        id: `anom_${Date.now()}`,
        type: 'anomalyDetected',
        source: 'SystemMonitor',
        data: { anomalies, metrics },
        timestamp: new Date(),
        priority: 'high'
      } as HanumanEvent);
    }
  }

  /**
   * Gestionnaires d'événements
   */
  private handleMetricsUpdate(metrics: PerformanceMetrics): void {
    this.currentStatus.performance = metrics;
    this.logger.debug('📊 Métriques mises à jour');
  }

  private handleAnomalyDetected(anomaly: any): void {
    this.logger.warn('🚨 Anomalie détectée:', anomaly);
    
    this.emit('systemAlert', {
      id: `alert_${Date.now()}`,
      type: 'systemAlert',
      source: 'SystemMonitor',
      data: { alert: anomaly },
      timestamp: new Date(),
      priority: 'high'
    } as HanumanEvent);
  }

  private handleHealthUpdate(healthData: any): void {
    this.logger.debug('💓 Mise à jour santé:', healthData);
  }

  private handleServiceDown(serviceInfo: any): void {
    this.logger.error('📴 Service indisponible:', serviceInfo);
    
    const alert: SystemAlert = {
      id: `svc_down_${Date.now()}`,
      level: serviceInfo.critical ? 'critical' : 'warning',
      message: `Service ${serviceInfo.name} indisponible`,
      timestamp: new Date(),
      source: 'SystemMonitor',
      resolved: false,
      actions: ['restart-service', 'check-dependencies', 'scale-up']
    };

    this.currentStatus.alerts.push(alert);
    this.alertManager.triggerAlert(alert);
  }

  private handleAlertTriggered(alert: SystemAlert): void {
    this.logger.warn('🚨 Alerte déclenchée:', alert.message);
    this.currentStatus.alerts.push(alert);
  }

  private handleAlertResolved(alertId: string): void {
    const alert = this.currentStatus.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.logger.info('✅ Alerte résolue:', alert.message);
    }
  }

  /**
   * Utilitaires
   */
  private calculateUptime(): number {
    // Calcul de l'uptime en secondes depuis le démarrage
    return Math.floor(process.uptime());
  }

  /**
   * Getters publics
   */
  public isActive(): boolean {
    return this.isActive;
  }

  public async getSystemStatus(): Promise<SystemStatus> {
    return this.currentStatus;
  }

  public getPerformanceMetrics(): PerformanceMetrics {
    return this.currentStatus.performance;
  }

  public getActiveAlerts(): SystemAlert[] {
    return this.currentStatus.alerts.filter(alert => !alert.resolved);
  }

  public getSystemHealth(): string {
    return this.currentStatus.health;
  }

  public async getDetailedReport(): Promise<any> {
    return {
      status: this.currentStatus,
      services: this.MONITORED_SERVICES,
      uptime: this.calculateUptime(),
      timestamp: new Date()
    };
  }
}

export default SystemMonitor;
