/**
 * 📡 Agent Communicator - Communication Inter-Agents
 * Gère la communication avec tous les agents du système Retreat & Be
 */

import { EventEmitter } from 'events';
import { Logger } from '../../utils/Logger';
import { MessageBus } from './MessageBus';
import { ServiceDiscovery } from './ServiceDiscovery';
import { 
  CommunicationConfig,
  AgentInfo,
  AgentRequest,
  AgentResponse,
  AgentWorkflow,
  WorkflowStep,
  HanumanEvent,
  AgentStatus
} from '../../types/HanumanTypes';

export class AgentCommunicator extends EventEmitter {
  private logger: Logger;
  private config: CommunicationConfig;
  private messageBus: MessageBus;
  private serviceDiscovery: ServiceDiscovery;
  private isActive: boolean = false;
  private activeAgents: Map<string, AgentInfo> = new Map();
  private pendingRequests: Map<string, AgentRequest> = new Map();
  private activeWorkflows: Map<string, AgentWorkflow> = new Map();

  // Mapping des agents Retreat & Be
  private readonly AGENT_ENDPOINTS = {
    'retreat-planner': 'http://agent-rb-service:5000/api/agents/retreat-planner',
    'partner-matcher': 'http://agent-rb-service:5000/api/agents/partner-matcher',
    'client-assistant': 'http://agent-rb-service:5000/api/agents/client-assistant',
    'content-moderator': 'http://agent-ia-service:5002/api/moderation',
    'analytics-engine': 'http://superagent-service:5001/api/analytics',
    'security-guardian': 'http://agent-ia-service:5002/api/security',
    'performance-optimizer': 'http://superagent-service:5001/api/optimizer',
    'coordinator': 'http://superagent-service:5001/api/coordinator',
    'supervisor': 'http://superagent-service:5001/api/supervisor'
  };

  constructor(config: CommunicationConfig) {
    super();
    this.config = config;
    this.logger = new Logger('AgentCommunicator');
    
    this.initializeComponents();
    this.setupEventHandlers();
    
    this.logger.info('📡 Agent Communicator initialisé');
  }

  /**
   * Initialise les composants de communication
   */
  private initializeComponents(): void {
    try {
      this.messageBus = new MessageBus(this.config);
      this.serviceDiscovery = new ServiceDiscovery(this.config);
      
      this.logger.info('✅ Composants de communication initialisés');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation des composants:', error);
      throw error;
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements du message bus
    this.messageBus.on('messageReceived', this.handleIncomingMessage.bind(this));
    this.messageBus.on('connectionLost', this.handleConnectionLost.bind(this));

    // Événements de découverte de services
    this.serviceDiscovery.on('agentDiscovered', this.handleAgentDiscovered.bind(this));
    this.serviceDiscovery.on('agentLost', this.handleAgentLost.bind(this));

    this.logger.info('🔗 Gestionnaires d\'événements configurés');
  }

  /**
   * Démarre le communicateur
   */
  public async start(): Promise<void> {
    try {
      this.logger.info('🚀 Démarrage du communicateur...');

      await this.messageBus.connect();
      await this.serviceDiscovery.start();
      
      // Découverte initiale des agents
      await this.discoverAgents();
      
      this.isActive = true;
      this.logger.info('✅ Agent Communicator démarré');
    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage du communicateur:', error);
      throw error;
    }
  }

  /**
   * Arrête le communicateur
   */
  public async stop(): Promise<void> {
    try {
      this.isActive = false;
      
      await this.serviceDiscovery.stop();
      await this.messageBus.disconnect();
      
      this.logger.info('🛑 Agent Communicator arrêté');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'arrêt du communicateur:', error);
      throw error;
    }
  }

  /**
   * Découvre tous les agents disponibles
   */
  private async discoverAgents(): Promise<void> {
    try {
      this.logger.info('🔍 Découverte des agents...');

      for (const [agentType, endpoint] of Object.entries(this.AGENT_ENDPOINTS)) {
        try {
          const agentInfo = await this.pingAgent(agentType, endpoint);
          if (agentInfo) {
            this.activeAgents.set(agentType, agentInfo);
            this.logger.info(`✅ Agent découvert: ${agentType}`);
          }
        } catch (error) {
          this.logger.warn(`⚠️ Agent non disponible: ${agentType}`);
        }
      }

      this.logger.info(`📊 ${this.activeAgents.size} agents découverts`);
    } catch (error) {
      this.logger.error('❌ Erreur lors de la découverte des agents:', error);
    }
  }

  /**
   * Ping un agent pour vérifier sa disponibilité
   */
  private async pingAgent(agentType: string, endpoint: string): Promise<AgentInfo | null> {
    try {
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        timeout: 5000
      });

      if (response.ok) {
        const healthData = await response.json();
        
        return {
          id: agentType,
          name: this.getAgentDisplayName(agentType),
          type: agentType as any,
          status: 'active' as AgentStatus,
          health: healthData.health || 100,
          load: healthData.load || 0,
          capabilities: healthData.capabilities || [],
          lastSeen: new Date(),
          endpoint
        };
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Envoie une requête à un agent spécifique
   */
  public async sendAgentRequest(request: AgentRequest): Promise<AgentResponse> {
    try {
      this.logger.info(`📤 Envoi requête à ${request.targetAgent}: ${request.action}`);

      // Vérification que l'agent est disponible
      const agent = this.activeAgents.get(request.targetAgent);
      if (!agent) {
        throw new Error(`Agent non disponible: ${request.targetAgent}`);
      }

      // Sauvegarde de la requête en attente
      this.pendingRequests.set(request.id, request);

      // Envoi via le message bus
      const response = await this.messageBus.sendRequest(agent.endpoint, {
        method: 'POST',
        path: `/api/agents/${request.targetAgent}/${request.action}`,
        data: request.payload,
        timeout: request.timeout,
        retries: request.retries
      });

      // Nettoyage de la requête en attente
      this.pendingRequests.delete(request.id);

      const agentResponse: AgentResponse = {
        requestId: request.id,
        agentId: request.targetAgent,
        status: response.success ? 'success' : 'error',
        data: response.data,
        error: response.error,
        timestamp: new Date(),
        processingTime: response.processingTime || 0
      };

      this.emit('agentResponse', {
        id: `resp_${Date.now()}`,
        type: 'agentResponse',
        source: 'AgentCommunicator',
        data: agentResponse,
        timestamp: new Date(),
        priority: 'medium'
      } as HanumanEvent);

      return agentResponse;
    } catch (error) {
      this.logger.error(`❌ Erreur lors de l'envoi de requête à ${request.targetAgent}:`, error);
      
      // Nettoyage de la requête en attente
      this.pendingRequests.delete(request.id);
      
      throw error;
    }
  }

  /**
   * Exécute un workflow multi-agents
   */
  public async executeWorkflow(workflow: AgentWorkflow): Promise<Map<string, AgentResponse>> {
    try {
      this.logger.info(`🎯 Exécution du workflow: ${workflow.name}`);

      this.activeWorkflows.set(workflow.id, workflow);
      const results = new Map<string, AgentResponse>();

      // Exécution séquentielle des étapes selon les dépendances
      const executedSteps = new Set<string>();
      
      while (executedSteps.size < workflow.steps.length) {
        const readySteps = workflow.steps.filter(step => 
          !executedSteps.has(step.id) && 
          this.areDependenciesMet(step.id, workflow, executedSteps)
        );

        if (readySteps.length === 0) {
          throw new Error('Deadlock détecté dans le workflow');
        }

        // Exécution parallèle des étapes prêtes
        const stepPromises = readySteps.map(step => this.executeWorkflowStep(step));
        const stepResults = await Promise.allSettled(stepPromises);

        // Traitement des résultats
        for (let i = 0; i < readySteps.length; i++) {
          const step = readySteps[i];
          const result = stepResults[i];

          if (result.status === 'fulfilled') {
            results.set(step.id, result.value);
            executedSteps.add(step.id);
            step.status = 'completed';
          } else {
            step.status = 'failed';
            this.logger.error(`❌ Échec de l'étape ${step.id}:`, result.reason);
          }
        }
      }

      workflow.status = 'completed';
      this.activeWorkflows.delete(workflow.id);

      this.logger.info(`✅ Workflow ${workflow.name} terminé avec ${results.size} étapes`);
      return results;
    } catch (error) {
      workflow.status = 'failed';
      this.logger.error(`❌ Erreur lors de l'exécution du workflow ${workflow.name}:`, error);
      throw error;
    }
  }

  /**
   * Exécute une étape de workflow
   */
  private async executeWorkflowStep(step: WorkflowStep): Promise<AgentResponse> {
    try {
      step.status = 'running';
      step.startTime = new Date();

      const request: AgentRequest = {
        id: `req_${step.id}_${Date.now()}`,
        targetAgent: step.agentId,
        action: step.action,
        payload: step.input,
        priority: 'medium',
        timeout: 30000,
        retries: 2
      };

      const response = await this.sendAgentRequest(request);
      
      step.output = response.data;
      step.endTime = new Date();
      
      return response;
    } catch (error) {
      step.endTime = new Date();
      throw error;
    }
  }

  /**
   * Vérifie si les dépendances d'une étape sont satisfaites
   */
  private areDependenciesMet(
    stepId: string, 
    workflow: AgentWorkflow, 
    executedSteps: Set<string>
  ): boolean {
    const dependency = workflow.dependencies.find(dep => dep.stepId === stepId);
    if (!dependency) return true;

    return dependency.dependsOn.every(depId => executedSteps.has(depId));
  }

  /**
   * Gestionnaires d'événements
   */
  private handleIncomingMessage(message: any): void {
    this.logger.debug('📨 Message reçu:', message);
    // Traitement des messages entrants des agents
  }

  private handleConnectionLost(): void {
    this.logger.warn('📡 Connexion au message bus perdue');
    this.emit('connectionLost');
  }

  private handleAgentDiscovered(agentInfo: AgentInfo): void {
    this.activeAgents.set(agentInfo.id, agentInfo);
    this.logger.info(`🔍 Nouvel agent découvert: ${agentInfo.name}`);
    
    this.emit('agentDiscovered', {
      id: `disc_${Date.now()}`,
      type: 'agentDiscovered',
      source: 'AgentCommunicator',
      data: agentInfo,
      timestamp: new Date(),
      priority: 'low'
    } as HanumanEvent);
  }

  private handleAgentLost(agentId: string): void {
    this.activeAgents.delete(agentId);
    this.logger.warn(`📴 Agent perdu: ${agentId}`);
    
    this.emit('agentOffline', {
      id: `lost_${Date.now()}`,
      type: 'agentOffline',
      source: 'AgentCommunicator',
      data: { agentId },
      timestamp: new Date(),
      priority: 'medium'
    } as HanumanEvent);
  }

  /**
   * Utilitaires
   */
  private getAgentDisplayName(agentType: string): string {
    const names: Record<string, string> = {
      'retreat-planner': 'Planificateur de Retraites',
      'partner-matcher': 'Matcher de Partenaires',
      'client-assistant': 'Assistant Client',
      'content-moderator': 'Modérateur de Contenu',
      'analytics-engine': 'Moteur d\'Analyse',
      'security-guardian': 'Gardien Sécurité',
      'performance-optimizer': 'Optimiseur Performance',
      'coordinator': 'Coordinateur',
      'supervisor': 'Superviseur'
    };
    
    return names[agentType] || agentType;
  }

  /**
   * Getters publics
   */
  public isActive(): boolean {
    return this.isActive;
  }

  public getActiveAgents(): AgentInfo[] {
    return Array.from(this.activeAgents.values());
  }

  public getAgentInfo(agentId: string): AgentInfo | undefined {
    return this.activeAgents.get(agentId);
  }

  public getPendingRequests(): AgentRequest[] {
    return Array.from(this.pendingRequests.values());
  }

  public getActiveWorkflows(): AgentWorkflow[] {
    return Array.from(this.activeWorkflows.values());
  }
}

export default AgentCommunicator;
