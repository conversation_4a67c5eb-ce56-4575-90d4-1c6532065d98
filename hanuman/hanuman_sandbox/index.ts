/**
 * 🏗️ SANDBOX HANUMAN - POINT D'ENTRÉE PRINCIPAL
 * ===============================================
 *
 * Environnement de développement et test sécurisé pour les agents d'Hanuman
 * Sprint 1: Infrastructure de base, sécurité et isolation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { EventEmitter } from 'events';
import { SandboxInfrastructure } from './infrastructure/sandbox_infrastructure';
import { SandboxSecurity } from './security/sandbox_security';
import { HanumanOrganOrchestrator } from '../../hanuman-working/services/HanumanOrganOrchestrator';
import { runSandboxTests } from './tests/infrastructure_tests';
import { HanumanIDE, HanumanIDEConfig } from './ide/index';

// Types principaux pour la sandbox
export interface SandboxConfig {
  autoStart: boolean;
  enableSecurity: boolean;
  enableMonitoring: boolean;
  enableTesting: boolean;
  enableIDE: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxContainers: number;
  defaultSecurityLevel: 'low' | 'medium' | 'high' | 'maximum';
  ideConfig?: Partial<HanumanIDEConfig>;
}

export interface SandboxStatus {
  isRunning: boolean;
  infrastructure: {
    initialized: boolean;
    containers: number;
    namespaces: number;
    networks: number;
  };
  security: {
    enabled: boolean;
    score: number;
    incidents: number;
    policies: number;
  };
  ide: {
    enabled: boolean;
    vscodeInstances: number;
    activeProjects: number;
    gitRepositories: number;
    runningSimulations: number;
  };
  performance: {
    uptime: number;
    responseTime: number;
    resourceUsage: {
      cpu: number;
      memory: number;
      storage: number;
    };
  };
}

/**
 * Classe principale de la Sandbox Hanuman
 * Orchestre l'infrastructure, la sécurité et les environnements
 */
export class HanumanSandbox extends EventEmitter {
  private config: SandboxConfig;
  private infrastructure: SandboxInfrastructure;
  private security: SandboxSecurity;
  private ide: HanumanIDE;
  private orchestrator: HanumanOrganOrchestrator;
  private isRunning = false;
  private startTime: Date | null = null;

  constructor(orchestrator: HanumanOrganOrchestrator, config?: Partial<SandboxConfig>) {
    super();

    this.orchestrator = orchestrator;
    this.config = {
      autoStart: true,
      enableSecurity: true,
      enableMonitoring: true,
      enableTesting: false,
      enableIDE: true,
      logLevel: 'info',
      maxContainers: 50,
      defaultSecurityLevel: 'medium',
      ideConfig: {
        vscode: { enabled: true, autoStart: true, defaultPort: 8080, extensions: ['ms-vscode.vscode-typescript-next', 'esbenp.prettier-vscode', 'ms-vscode.vscode-eslint', 'roo-coder.roo-coder'] },
        rooCoder: { enabled: true, model: 'gpt-4', customPrompts: true },
        templates: { enabled: true, autoLoad: true, customTemplates: true },
        git: { enabled: true, autoCommit: true, autoCommitInterval: 15 },
        simulator: { enabled: true, hotReload: true, monitoring: true }
      },
      ...config
    };

    this.log('info', '🏗️ Initialisation de la Sandbox Hanuman...');
    this.initializeSandbox();
  }

  /**
   * Initialise tous les composants de la sandbox
   */
  private async initializeSandbox(): Promise<void> {
    try {
      // Initialiser l'infrastructure
      this.infrastructure = new SandboxInfrastructure(this.orchestrator);
      this.log('info', '✅ Infrastructure initialisée');

      // Initialiser la sécurité si activée
      if (this.config.enableSecurity) {
        this.security = new SandboxSecurity(this.infrastructure);
        this.log('info', '🛡️ Système de sécurité initialisé');
      }

      // Initialiser l'IDE si activé
      if (this.config.enableIDE) {
        this.ide = new HanumanIDE(this.infrastructure, this.config.ideConfig);
        this.log('info', '💻 IDE Hanuman initialisé');
      }

      // Configurer les événements
      this.setupEventHandlers();

      // Démarrage automatique si configuré
      if (this.config.autoStart) {
        await this.start();
      }

      this.emit('sandbox:initialized');
      this.log('info', '🎉 Sandbox Hanuman initialisée avec succès');

    } catch (error) {
      this.log('error', `❌ Erreur lors de l'initialisation: ${error}`);
      this.emit('sandbox:error', error);
      throw error;
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements de l'infrastructure
    this.infrastructure.on('infrastructure:initialized', () => {
      this.log('debug', 'Infrastructure prête');
    });

    this.infrastructure.on('container:created', (container) => {
      this.log('info', `📦 Conteneur créé: ${container.name}`);
      this.emit('sandbox:container-created', container);
    });

    this.infrastructure.on('container:destroyed', (container) => {
      this.log('info', `🗑️ Conteneur détruit: ${container.name}`);
      this.emit('sandbox:container-destroyed', container);
    });

    // Événements de sécurité
    if (this.security) {
      this.security.on('security:incident-created', (incident) => {
        this.log('warn', `🚨 Incident de sécurité: ${incident.description}`);
        this.emit('sandbox:security-incident', incident);
      });

      this.security.on('security:scan-completed', (metrics) => {
        this.log('debug', `🔍 Scan de sécurité terminé - Score: ${metrics.securityScore}%`);
        this.emit('sandbox:security-scan', metrics);
      });
    }

    // Événements de l'IDE
    if (this.ide) {
      this.ide.on('ide:project-created', (data) => {
        this.log('info', `🚀 Projet créé: ${data.container.name}`);
        this.emit('sandbox:project-created', data);
      });

      this.ide.on('ide:vscode-deployed', (instance) => {
        this.log('info', `💻 VS Code déployé: ${instance.url}`);
        this.emit('sandbox:vscode-deployed', instance);
      });

      this.ide.on('ide:error', (error) => {
        this.log('error', `❌ Erreur IDE: ${error}`);
        this.emit('sandbox:ide-error', error);
      });
    }

    // Événements de l'orchestrateur
    this.orchestrator.on('neural:signal-generated', (data) => {
      this.log('debug', `🧠 Signal neural reçu de l'organe ${data.signal.source}`);
    });
  }

  /**
   * Démarre la sandbox
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.log('warn', '⚠️ Sandbox déjà en cours d\'exécution');
      return;
    }

    try {
      this.log('info', '🚀 Démarrage de la Sandbox Hanuman...');

      this.isRunning = true;
      this.startTime = new Date();

      // Démarrer le monitoring si activé
      if (this.config.enableMonitoring) {
        this.startMonitoring();
      }

      this.emit('sandbox:started');
      this.log('info', '✅ Sandbox Hanuman démarrée avec succès');

    } catch (error) {
      this.isRunning = false;
      this.log('error', `❌ Erreur lors du démarrage: ${error}`);
      this.emit('sandbox:error', error);
      throw error;
    }
  }

  /**
   * Arrête la sandbox
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.log('warn', '⚠️ Sandbox déjà arrêtée');
      return;
    }

    try {
      this.log('info', '🛑 Arrêt de la Sandbox Hanuman...');

      // Arrêter la sécurité
      if (this.security) {
        this.security.stopSecurityMonitoring();
      }

      // Détruire tous les conteneurs
      const containers = this.infrastructure.getContainers();
      for (const container of containers) {
        await this.infrastructure.destroyContainer(container.id);
      }

      this.isRunning = false;
      this.startTime = null;

      this.emit('sandbox:stopped');
      this.log('info', '✅ Sandbox Hanuman arrêtée');

    } catch (error) {
      this.log('error', `❌ Erreur lors de l'arrêt: ${error}`);
      this.emit('sandbox:error', error);
      throw error;
    }
  }

  /**
   * Redémarre la sandbox
   */
  async restart(): Promise<void> {
    this.log('info', '🔄 Redémarrage de la Sandbox Hanuman...');
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Attendre 2 secondes
    await this.start();
  }

  /**
   * Démarre le monitoring
   */
  private startMonitoring(): void {
    // Monitoring des performances toutes les minutes
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 60000);

    this.log('debug', '📊 Monitoring démarré');
  }

  /**
   * Collecte les métriques de performance
   */
  private collectPerformanceMetrics(): void {
    const status = this.getStatus();

    this.emit('sandbox:metrics', {
      timestamp: new Date(),
      containers: status.infrastructure.containers,
      securityScore: status.security.score,
      resourceUsage: status.performance.resourceUsage,
      uptime: status.performance.uptime
    });
  }

  /**
   * Crée un nouvel environnement de développement
   */
  async createDevelopmentEnvironment(config: {
    name: string;
    agentId?: string;
    organId?: string;
    securityLevel?: 'low' | 'medium' | 'high' | 'maximum';
  }): Promise<any> {
    this.log('info', `🧪 Création d'un environnement de développement: ${config.name}`);

    try {
      const container = await this.infrastructure.createContainer({
        name: config.name,
        type: 'development',
        namespace: 'development',
        agentId: config.agentId,
        organId: config.organId,
        securityLevel: config.securityLevel || this.config.defaultSecurityLevel
      });

      this.log('info', `✅ Environnement créé: ${config.name}`);
      return container;

    } catch (error) {
      this.log('error', `❌ Erreur lors de la création de l'environnement: ${error}`);
      throw error;
    }
  }

  /**
   * Crée un projet de développement complet avec IDE
   */
  async createDevelopmentProject(config: {
    name: string;
    agentId?: string;
    organId?: string;
    templateId?: string;
    gitRemote?: string;
    securityLevel?: 'low' | 'medium' | 'high' | 'maximum';
  }): Promise<any> {
    if (!this.config.enableIDE || !this.ide) {
      throw new Error('IDE non activé - utilisez createDevelopmentEnvironment() à la place');
    }

    this.log('info', `🚀 Création d'un projet de développement complet: ${config.name}`);

    try {
      const result = await this.ide.createDevelopmentProject({
        name: config.name,
        agentId: config.agentId,
        organId: config.organId,
        templateId: config.templateId,
        gitRemote: config.gitRemote,
        autoStart: true
      });

      this.log('info', `✅ Projet de développement créé: ${config.name}`);
      this.log('info', `💻 VS Code disponible sur: ${result.vscodeInstance.url}`);

      return result;

    } catch (error) {
      this.log('error', `❌ Erreur lors de la création du projet: ${error}`);
      throw error;
    }
  }

  /**
   * Lance les tests d'infrastructure
   */
  async runTests(): Promise<void> {
    if (!this.config.enableTesting) {
      this.log('warn', '⚠️ Tests désactivés dans la configuration');
      return;
    }

    this.log('info', '🧪 Lancement des tests d\'infrastructure...');

    try {
      await runSandboxTests();
      this.log('info', '✅ Tests terminés');
    } catch (error) {
      this.log('error', `❌ Erreur lors des tests: ${error}`);
      throw error;
    }
  }

  /**
   * Obtient le statut complet de la sandbox
   */
  getStatus(): SandboxStatus {
    const infraStats = this.infrastructure.getInfrastructureStats();
    const securityMetrics = this.security?.getSecurityMetrics();
    const ideStats = this.ide?.getStats();

    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;

    return {
      isRunning: this.isRunning,
      infrastructure: {
        initialized: infraStats.isInitialized,
        containers: infraStats.containers.total,
        namespaces: infraStats.namespaces,
        networks: infraStats.networks
      },
      security: {
        enabled: this.config.enableSecurity,
        score: securityMetrics?.securityScore || 100,
        incidents: securityMetrics?.totalIncidents || 0,
        policies: securityMetrics?.policiesActive || 0
      },
      ide: {
        enabled: this.config.enableIDE,
        vscodeInstances: ideStats?.vscodeInstances || 0,
        activeProjects: ideStats?.activeProjects || 0,
        gitRepositories: ideStats?.gitRepositories || 0,
        runningSimulations: ideStats?.runningSimulations || 0
      },
      performance: {
        uptime: uptime,
        responseTime: 0, // À implémenter
        resourceUsage: {
          cpu: 0, // À calculer depuis les conteneurs
          memory: 0, // À calculer depuis les conteneurs
          storage: 0 // À calculer depuis les conteneurs
        }
      }
    };
  }

  /**
   * Obtient les composants de la sandbox
   */
  getComponents() {
    return {
      infrastructure: this.infrastructure,
      security: this.security,
      ide: this.ide,
      orchestrator: this.orchestrator
    };
  }

  /**
   * Met à jour la configuration
   */
  updateConfig(newConfig: Partial<SandboxConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('info', '⚙️ Configuration mise à jour');
    this.emit('sandbox:config-updated', this.config);
  }

  /**
   * Obtient la configuration actuelle
   */
  getConfig(): SandboxConfig {
    return { ...this.config };
  }

  /**
   * Fonction de logging avec niveaux
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    const configLevel = levels[this.config.logLevel];
    const messageLevel = levels[level];

    if (messageLevel >= configLevel) {
      const timestamp = new Date().toISOString();
      const prefix = {
        debug: '🔍',
        info: 'ℹ️',
        warn: '⚠️',
        error: '❌'
      }[level];

      console.log(`[${timestamp}] ${prefix} ${message}`);
    }
  }
}

// Exports principaux
export { SandboxInfrastructure } from './infrastructure/sandbox_infrastructure';
export { SandboxSecurity } from './security/sandbox_security';
export { EnvironmentManager } from './environments/environment_manager';
export { SandboxManagementInterface } from './interfaces/sandbox_management_interface';
export { InfrastructureTests, runSandboxTests } from './tests/infrastructure_tests';

// Exports IDE (Sprint 2)
export { HanumanIDE, createHanumanIDE } from './ide/index';
export { IDEInterface } from './ide/ide_interface';
export { VSCodeServerManager, RooCoderIntegration } from './ide/index';
export { TemplateManager, TemplateInterface } from './ide/index';
export { GitManager, EnvironmentSimulator } from './ide/index';

// Exports QA (Sprint 5)
export { QAValidatorAgent } from './qa/qa_validator_agent';
export { UITestingFramework } from './qa/ui_testing_framework';
export { PerformanceValidator } from './qa/performance_validator';
export { QAReportingSystem } from './qa/qa_reporting_system';
export { TestScenarioGenerator } from './qa/test_scenario_generator';
export { QAValidationDashboard } from './interfaces/qa_validation_dashboard';

// Exports Roadmap System (Système Obligatoire)
export { RoadmapGenerator } from './roadmap/roadmap_generator';
export { RoadmapValidatorAgent } from './roadmap/roadmap_validator_agent';
export { RoadmapTracker } from './roadmap/roadmap_tracker';
export { DeploymentGateKeeper } from './roadmap/deployment_gate_keeper';
export { RoadmapManagementDashboard } from './interfaces/roadmap_management_dashboard';

// Exports Deployment (Sprint 6)
export { DeploymentOrchestrator } from './deployment/deployment_orchestrator';
export { CICDPipeline } from './deployment/cicd_pipeline';
export { VersionManager } from './deployment/version_manager';
export { RollbackSystem } from './deployment/rollback_system';
export { DeploymentMonitoring } from './deployment/deployment_monitoring';
export { DeploymentPipelineInterface } from './interfaces/deployment_pipeline_interface';

// Export par défaut
export default HanumanSandbox;

/**
 * Fonction utilitaire pour créer une instance de sandbox
 */
export function createHanumanSandbox(
  orchestrator: HanumanOrganOrchestrator,
  config?: Partial<SandboxConfig>
): HanumanSandbox {
  return new HanumanSandbox(orchestrator, config);
}

/**
 * Fonction utilitaire pour démarrer une sandbox de test
 */
export async function startTestSandbox(): Promise<HanumanSandbox> {
  // Mock orchestrator pour les tests
  const mockOrchestrator = {
    emit: (event: string, data: any) => console.log(`Mock Event: ${event}`),
    on: (event: string, handler: Function) => {},
    off: (event: string, handler: Function) => {}
  } as any;

  const sandbox = new HanumanSandbox(mockOrchestrator, {
    enableTesting: true,
    logLevel: 'debug'
  });

  await sandbox.start();
  return sandbox;
}
