version: '3.8'

services:
  # Infrastructure Services
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - cortex-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      K<PERSON><PERSON>_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - cortex-network

  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - cortex-network

  weaviate:
    image: semitechnologies/weaviate:1.22.4
    hostname: weaviate
    container_name: weaviate
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate-data:/var/lib/weaviate
    networks:
      - cortex-network

  # Monitoring Services
  prometheus:
    image: prom/prometheus:v2.47.0
    hostname: prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - cortex-network

  grafana:
    image: grafana/grafana:10.1.0
    hostname: grafana
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: cortex-admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - cortex-network

  # Cortex Central - Main Orchestrator
  cortex-central:
    build:
      context: ./cortex-central
      dockerfile: Dockerfile
    hostname: cortex-central
    container_name: cortex-central
    ports:
      - "8080:8080"
      - "3010:3010"  # Dashboard WebSocket
    environment:
      NODE_ENV: production
      PORT: 8080
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      NEURAL_NETWORK_MODE: active
      DECISION_ENGINE: enabled
      MONITORING_ENABLED: true
      LOG_LEVEL: info
      CORS_ORIGIN: "*"
    depends_on:
      - kafka
      - redis
      - weaviate
    volumes:
      - cortex-logs:/app/logs
      - cortex-memory:/app/memory
      - cortex-workspace:/app/workspace
    networks:
      - cortex-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Agent Frontend
  agent-frontend:
    build:
      context: ./agents/frontend
      dockerfile: Dockerfile
    hostname: agent-frontend
    container_name: agent-frontend
    ports:
      - "3002:3002"
    environment:
      NODE_ENV: production
      PORT: 3002
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-frontend-001
    depends_on:
      - cortex-central
    volumes:
      - frontend-workspace:/app/workspace
      - frontend-logs:/app/logs
    networks:
      - cortex-network
    restart: unless-stopped

  # Agent Backend
  agent-backend:
    build:
      context: ./agents/backend
      dockerfile: Dockerfile
    hostname: agent-backend
    container_name: agent-backend
    ports:
      - "3003:3003"
    environment:
      NODE_ENV: production
      PORT: 3003
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-backend-001
    depends_on:
      - cortex-central
    volumes:
      - backend-workspace:/app/workspace
      - backend-logs:/app/logs
    networks:
      - cortex-network
    restart: unless-stopped

  # Agent UI/UX
  agent-uiux:
    build:
      context: ./agents/uiux
      dockerfile: Dockerfile
    hostname: agent-uiux
    container_name: agent-uiux
    ports:
      - "3004:3004"
    environment:
      NODE_ENV: production
      PORT: 3004
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-uiux-001
    depends_on:
      - cortex-central
    volumes:
      - uiux-workspace:/app/workspace
      - uiux-logs:/app/logs
    networks:
      - cortex-network
    restart: unless-stopped

  # Agent QA
  agent-qa:
    build:
      context: ./agents/qa
      dockerfile: Dockerfile
    hostname: agent-qa
    container_name: agent-qa
    ports:
      - "3008:3008"
    environment:
      NODE_ENV: production
      PORT: 3008
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-qa-001
    depends_on:
      - cortex-central
    volumes:
      - qa-workspace:/app/workspace
      - qa-logs:/app/logs
      - qa-reports:/app/reports
    networks:
      - cortex-network
    restart: unless-stopped

  # Agent DevOps
  agent-devops:
    build:
      context: ./agents/devops
      dockerfile: Dockerfile
    hostname: agent-devops
    container_name: agent-devops
    ports:
      - "3009:3009"
    environment:
      NODE_ENV: production
      PORT: 3009
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-devops-001
      DOCKER_HOST: unix:///var/run/docker.sock
    depends_on:
      - cortex-central
    volumes:
      - devops-workspace:/app/workspace
      - devops-logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
      - devops-terraform:/app/terraform
    networks:
      - cortex-network
    restart: unless-stopped

  # Agent Security
  agent-security:
    build:
      context: ./agents/security
      dockerfile: Dockerfile
    hostname: agent-security
    container_name: agent-security
    ports:
      - "3007:3007"
    environment:
      NODE_ENV: production
      PORT: 3007
      KAFKA_BROKERS: kafka:29092
      REDIS_URL: redis://redis:6379
      WEAVIATE_URL: http://weaviate:8080
      CORTEX_CENTRAL_URL: http://cortex-central:8080
      AGENT_ID: agent-security-001
      LOG_LEVEL: info
      SECURITY_SCAN_DEPTH: comprehensive
      COMPLIANCE_FRAMEWORKS: owasp,cis,nist
      THREAT_INTELLIGENCE_ENABLED: true
      INCIDENT_RESPONSE_ENABLED: true
      MAX_CONCURRENT_SCANS: 3
      SCAN_TIMEOUT: 300000
    depends_on:
      - cortex-central
    volumes:
      - security-workspace:/app/workspace
      - security-logs:/app/logs
      - security-reports:/app/reports
      - security-data:/app/data
    networks:
      - cortex-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3007/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Dashboard Web Interface
  cortex-dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
    hostname: cortex-dashboard
    container_name: cortex-dashboard
    ports:
      - "3000:3000"
    environment:
      REACT_APP_CORTEX_URL: http://localhost:8080
      REACT_APP_WEBSOCKET_URL: ws://localhost:8080
    depends_on:
      - cortex-central
    networks:
      - cortex-network
    restart: unless-stopped

volumes:
  # Infrastructure
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  redis-data:
  weaviate-data:
  prometheus-data:
  grafana-data:

  # Cortex Central
  cortex-logs:
  cortex-memory:
  cortex-workspace:

  # Agents
  frontend-workspace:
  frontend-logs:
  backend-workspace:
  backend-logs:
  uiux-workspace:
  uiux-logs:
  qa-workspace:
  qa-logs:
  qa-reports:
  devops-workspace:
  devops-logs:
  devops-terraform:
  security-workspace:
  security-logs:
  security-reports:
  security-data:

networks:
  cortex-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
