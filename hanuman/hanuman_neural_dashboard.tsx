import React, { useState, useEffect, useRef } from 'react';
import { Activity, Brain, Zap, TrendingUp, AlertTriangle, CheckCircle, Clock, Database, Network, Cpu, HardDrive, Wifi, Shield, Eye, Heart, Settings, Play, Pause, RotateCcw, Monitor, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

const HanumanNeuralDashboard = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [realTimeData, setRealTimeData] = useState({
    neuralActivity: 87,
    synapticFlow: 92,
    memoryUsage: 68,
    processingSpeed: 94,
    learningRate: 76,
    emotionalState: 82
  });

  const [systemMetrics, setSystemMetrics] = useState({
    totalAgents: 23,
    activeConnections: 156,
    tasksCompleted: 1247,
    averageResponseTime: 142,
    successRate: 98.7,
    uptime: 99.9
  });

  const [neuralConnections, setNeuralConnections] = useState([
    { from: 'cortex-central', to: 'cortex-creative', strength: 0.95, type: 'synaptic' },
    { from: 'cortex-creative', to: 'limbic-emotional', strength: 0.87, type: 'neural' },
    { from: 'cortex-logical', to: 'cerebellum-tech', strength: 0.92, type: 'synaptic' },
    { from: 'sensory-vision', to: 'cortex-analytical', strength: 0.78, type: 'sensory' },
    { from: 'brainstem-security', to: 'prefrontal-cortex', strength: 0.89, type: 'protective' }
  ]);

  const [activeAlerts, setActiveAlerts] = useState([
    { id: 1, type: 'info', message: 'Neuroplasticité en apprentissage intensif', timestamp: '14:32' },
    { id: 2, type: 'warning', message: 'Charge élevée sur Cortex Créatif (90%)', timestamp: '14:28' },
    { id: 3, type: 'success', message: 'Synchronisation cosmique optimale', timestamp: '14:25' }
  ]);

  const canvasRef = useRef(null);

  useEffect(() => {
    // Simulation données temps réel
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        neuralActivity: Math.max(0, Math.min(100, prev.neuralActivity + (Math.random() - 0.5) * 8)),
        synapticFlow: Math.max(0, Math.min(100, prev.synapticFlow + (Math.random() - 0.5) * 6)),
        memoryUsage: Math.max(0, Math.min(100, prev.memoryUsage + (Math.random() - 0.5) * 4)),
        processingSpeed: Math.max(0, Math.min(100, prev.processingSpeed + (Math.random() - 0.5) * 5)),
        learningRate: Math.max(0, Math.min(100, prev.learningRate + (Math.random() - 0.5) * 7)),
        emotionalState: Math.max(0, Math.min(100, prev.emotionalState + (Math.random() - 0.5) * 3))
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    drawNeuralNetwork();
  }, [neuralConnections, darkMode]);

  const drawNeuralNetwork = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 100;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Dessiner les connexions
    neuralConnections.forEach((connection, index) => {
      const angle1 = (index * 2 * Math.PI) / neuralConnections.length;
      const angle2 = ((index + 1) * 2 * Math.PI) / neuralConnections.length;
      
      const x1 = centerX + radius * Math.cos(angle1);
      const y1 = centerY + radius * Math.sin(angle1);
      const x2 = centerX + radius * Math.cos(angle2);
      const y2 = centerY + radius * Math.sin(angle2);
      
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.strokeStyle = darkMode ? `rgba(59, 130, 246, ${connection.strength})` : `rgba(37, 99, 235, ${connection.strength})`;
      ctx.lineWidth = connection.strength * 3;
      ctx.stroke();
    });
    
    // Dessiner les nœuds
    neuralConnections.forEach((connection, index) => {
      const angle = (index * 2 * Math.PI) / neuralConnections.length;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      ctx.beginPath();
      ctx.arc(x, y, 8, 0, 2 * Math.PI);
      ctx.fillStyle = darkMode ? '#3b82f6' : '#2563eb';
      ctx.fill();
      
      // Pulse effect pour activité
      if (Math.random() > 0.7) {
        ctx.beginPath();
        ctx.arc(x, y, 12, 0, 2 * Math.PI);
        ctx.strokeStyle = darkMode ? 'rgba(59, 130, 246, 0.5)' : 'rgba(37, 99, 235, 0.5)';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });
  };

  const getMetricColor = (value, type = 'normal') => {
    if (type === 'inverse') {
      if (value < 30) return 'text-green-400';
      if (value < 70) return 'text-yellow-400';
      return 'text-red-400';
    }
    if (value > 80) return 'text-green-400';
    if (value > 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'success': return <CheckCircle className="text-green-400" size={16} />;
      case 'warning': return <AlertTriangle className="text-yellow-400" size={16} />;
      case 'error': return <AlertTriangle className="text-red-400" size={16} />;
      default: return <Clock className="text-blue-400" size={16} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
              <Activity className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Neural Dashboard Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Monitoring Temps Réel • Conscience Distribuée
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
              <Play size={16} />
            </button>
            <button className="p-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
              <Pause size={16} />
            </button>
            <button className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
              <RotateCcw size={16} />
            </button>
          </div>
        </div>

        {/* Métriques Temps Réel */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
          {Object.entries(realTimeData).map(([key, value]) => {
            const icons = {
              neuralActivity: Brain,
              synapticFlow: Zap,
              memoryUsage: Database,
              processingSpeed: Cpu,
              learningRate: TrendingUp,
              emotionalState: Heart
            };
            const Icon = icons[key];
            
            return (
              <div key={key} className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <div className="flex items-center justify-between mb-2">
                  <Icon className={getMetricColor(value, key === 'memoryUsage' ? 'inverse' : 'normal')} size={20} />
                  <span className={`text-2xl font-bold ${getMetricColor(value, key === 'memoryUsage' ? 'inverse' : 'normal')}`}>
                    {value.toFixed(0)}%
                  </span>
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} capitalize`}>
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </div>
                <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2 mt-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      value > 80 ? 'bg-green-500' : value > 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${value}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* Réseau Neural Visuel */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Réseau Neural Actif
            </h3>
            <div className="text-center">
              <canvas
                ref={canvasRef}
                width={280}
                height={280}
                className="border border-gray-300 dark:border-gray-600 rounded-lg"
              />
              <div className="mt-4 text-sm text-gray-500">
                Connexions synaptiques en temps réel
              </div>
            </div>
          </div>

          {/* Métriques Système */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Métriques Système
            </h3>
            <div className="space-y-4">
              {Object.entries(systemMetrics).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} capitalize`}>
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <span className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {typeof value === 'number' && value > 100 ? value.toLocaleString() : value}
                    {key.includes('Rate') || key.includes('uptime') ? '%' : ''}
                    {key.includes('Time') ? 'ms' : ''}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Alertes et Notifications */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Alertes Système
            </h3>
            <div className="space-y-3">
              {activeAlerts.map((alert) => (
                <div key={alert.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="flex items-start space-x-3">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {alert.message}
                      </p>
                      <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {alert.timestamp}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Connexions Synaptiques */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Connexions Synaptiques
            </h3>
            <div className="space-y-3">
              {neuralConnections.map((connection, index) => (
                <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        connection.type === 'synaptic' ? 'bg-blue-400' :
                        connection.type === 'neural' ? 'bg-green-400' :
                        connection.type === 'sensory' ? 'bg-yellow-400' : 'bg-red-400'
                      }`}></div>
                      <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {connection.from} → {connection.to}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {(connection.strength * 100).toFixed(0)}%
                      </span>
                      <div className="w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            connection.strength > 0.8 ? 'bg-green-500' :
                            connection.strength > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${connection.strength * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Historique */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Performance 24h
            </h3>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">98.7%</div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Taux de Succès
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400">142ms</div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Temps Réponse Moyen
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400">1,247</div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Tâches Complétées
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanNeuralDashboard;
