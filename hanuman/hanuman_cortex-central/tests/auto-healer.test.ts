import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AIImmuneSystem } from '../src/immune/AIImmuneSystem';
import { AutoHealer } from '../src/immune/AutoHealer';
import { AnomalyDetector } from '../src/immune/AnomalyDetector';
import { ServiceController } from '../src/immune/ServiceController';
import { HealingStrategies } from '../src/immune/HealingStrategies';
import { AdaptationEngine } from '../src/immune/AdaptationEngine';
import {
  Anomaly,
  HealingStrategy,
  HealthMetrics,
  ImmuneSystemConfig,
  AutoHealerConfig
} from '../src/immune/types';

// Mocks
const mockMemory = {
  store: jest.fn(),
  retrieve: jest.fn(),
  delete: jest.fn()
};

const mockCommunication = {
  broadcast: jest.fn(),
  send: jest.fn(),
  subscribe: jest.fn()
};

describe('Auto-Healer System', () => {
  let immuneSystem: AIImmuneSystem;
  let autoHealer: AutoHealer;
  let anomalyDetector: AnomalyDetector;
  let serviceController: ServiceController;
  let healingStrategies: HealingStrategies;
  let adaptationEngine: AdaptationEngine;

  const mockConfig: ImmuneSystemConfig = {
    memory: mockMemory as any,
    communication: mockCommunication as any,
    monitoringInterval: 1000,
    healingEnabled: true,
    adaptationEnabled: true
  };

  const mockAutoHealerConfig: AutoHealerConfig = {
    memory: mockMemory as any,
    communication: mockCommunication as any,
    maxConcurrentHealings: 2,
    defaultTimeout: 30000,
    riskThreshold: 0.7,
    enableLearning: true,
    enableRollback: true
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Mock des méthodes de mémoire
    mockMemory.retrieve.mockResolvedValue(null);
    mockMemory.store.mockResolvedValue(true);
    
    // Initialisation des composants
    immuneSystem = new AIImmuneSystem(mockConfig);
    autoHealer = new AutoHealer(mockAutoHealerConfig);
    anomalyDetector = new AnomalyDetector({
      memory: mockMemory as any,
      sensitivityLevel: 0.7
    });
    serviceController = new ServiceController({
      memory: mockMemory as any,
      communication: mockCommunication as any
    });
    healingStrategies = new HealingStrategies({
      memory: mockMemory as any
    });
    adaptationEngine = new AdaptationEngine({
      memory: mockMemory as any,
      learningRate: 0.1
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('AIImmuneSystem', () => {
    it('should initialize successfully', async () => {
      await expect(immuneSystem.initialize()).resolves.not.toThrow();
      expect(immuneSystem.isInitialized()).toBe(true);
    });

    it('should detect and handle anomalies', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-1',
        type: 'performance_degradation',
        severity: 'high',
        description: 'High response time detected',
        metrics: { responseTime: 5000 },
        timestamp: new Date(),
        affectedComponents: ['api-service'],
        confidence: 0.9
      };

      const healingSpy = jest.spyOn(autoHealer, 'apply');
      healingSpy.mockResolvedValue({
        success: true,
        strategy: {} as HealingStrategy,
        anomaly: mockAnomaly,
        executedActions: [],
        startTime: new Date(),
        endTime: new Date()
      });

      await immuneSystem.initialize();
      
      // Simulation d'une anomalie détectée
      immuneSystem.emit('anomaly-detected', { anomaly: mockAnomaly });
      
      // Vérification que la guérison a été tentée
      expect(healingSpy).toHaveBeenCalled();
    });

    it('should collect health metrics', async () => {
      await immuneSystem.initialize();
      
      const metrics = await immuneSystem.gatherMetrics();
      
      expect(metrics).toHaveProperty('timestamp');
      expect(metrics).toHaveProperty('systemLoad');
      expect(metrics).toHaveProperty('memoryUsage');
      expect(metrics).toHaveProperty('responseTime');
      expect(metrics).toHaveProperty('errorRate');
    });
  });

  describe('AutoHealer', () => {
    beforeEach(async () => {
      await autoHealer.initialize();
    });

    it('should select appropriate healing strategy', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-2',
        type: 'performance_degradation',
        severity: 'medium',
        description: 'Service response time degraded',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['web-service'],
        confidence: 0.8
      };

      const strategy = await autoHealer.selectStrategy(mockAnomaly);
      
      expect(strategy).toBeDefined();
      expect(strategy?.applicableAnomalies).toContain('performance_degradation');
    });

    it('should apply healing strategy successfully', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-3',
        type: 'high_error_rate',
        severity: 'high',
        description: 'Error rate above threshold',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['api-service'],
        confidence: 0.9
      };

      const mockStrategy: HealingStrategy = {
        id: 'test-strategy',
        name: 'Test Strategy',
        description: 'Test healing strategy',
        applicableAnomalies: ['high_error_rate'],
        actions: [{
          type: 'restart',
          target: 'api-service',
          parameters: {},
          timeout: 30000
        }],
        estimatedRecoveryTime: 60000,
        riskLevel: 0.3
      };

      // Mock de l'exécution d'action
      const executeActionSpy = jest.spyOn(serviceController, 'restartService');
      executeActionSpy.mockResolvedValue();

      const result = await autoHealer.apply(mockStrategy, mockAnomaly);
      
      expect(result.success).toBe(true);
      expect(result.strategy).toBe(mockStrategy);
      expect(result.anomaly).toBe(mockAnomaly);
    });

    it('should respect risk threshold', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-4',
        type: 'security_breach',
        severity: 'critical',
        description: 'Security violation detected',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['auth-service'],
        confidence: 0.95
      };

      // Mock d'une stratégie à haut risque
      const highRiskStrategy: HealingStrategy = {
        id: 'high-risk-strategy',
        name: 'High Risk Strategy',
        description: 'Dangerous strategy',
        applicableAnomalies: ['security_breach'],
        actions: [],
        estimatedRecoveryTime: 60000,
        riskLevel: 0.9 // Au-dessus du seuil de 0.7
      };

      jest.spyOn(healingStrategies, 'findApplicableStrategies')
        .mockResolvedValue([highRiskStrategy]);

      const strategy = await autoHealer.selectStrategy(mockAnomaly);
      
      expect(strategy).toBeNull(); // Stratégie rejetée à cause du risque élevé
    });

    it('should perform rollback on failure', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-5',
        type: 'performance_degradation',
        severity: 'medium',
        description: 'Performance issue',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['service-1'],
        confidence: 0.8
      };

      const mockStrategy: HealingStrategy = {
        id: 'failing-strategy',
        name: 'Failing Strategy',
        description: 'Strategy that will fail',
        applicableAnomalies: ['performance_degradation'],
        actions: [{
          type: 'restart',
          target: 'service-1',
          parameters: {},
          timeout: 30000
        }],
        estimatedRecoveryTime: 60000,
        riskLevel: 0.3,
        rollbackActions: [{
          type: 'restart',
          target: 'service-1',
          parameters: { forceRestart: true },
          timeout: 30000
        }]
      };

      // Mock d'un échec d'action
      const executeActionSpy = jest.spyOn(serviceController, 'restartService');
      executeActionSpy.mockRejectedValue(new Error('Service restart failed'));

      const result = await autoHealer.apply(mockStrategy, mockAnomaly);
      
      expect(result.success).toBe(false);
      expect(result.rollbackPerformed).toBe(true);
    });
  });

  describe('AnomalyDetector', () => {
    beforeEach(async () => {
      await anomalyDetector.initialize();
    });

    it('should detect performance anomalies', async () => {
      const currentMetrics: HealthMetrics = {
        timestamp: new Date(),
        systemLoad: 95, // Charge élevée
        memoryUsage: 85,
        responseTime: 3000, // Temps de réponse élevé
        errorRate: 2,
        agentHealth: new Map(),
        synapticHealth: 70,
        throughput: 100
      };

      const metricsHistory: HealthMetrics[] = [
        {
          timestamp: new Date(Date.now() - 60000),
          systemLoad: 50,
          memoryUsage: 60,
          responseTime: 200,
          errorRate: 0.1,
          agentHealth: new Map(),
          synapticHealth: 90,
          throughput: 500
        }
      ];

      const anomalies = await anomalyDetector.detect(currentMetrics, metricsHistory);
      
      expect(anomalies.length).toBeGreaterThan(0);
      expect(anomalies.some(a => a.type === 'performance_degradation')).toBe(true);
    });

    it('should detect resource exhaustion', async () => {
      const currentMetrics: HealthMetrics = {
        timestamp: new Date(),
        systemLoad: 98,
        memoryUsage: 97, // Mémoire presque épuisée
        responseTime: 1000,
        errorRate: 1,
        agentHealth: new Map(),
        synapticHealth: 60,
        throughput: 50
      };

      const anomalies = await anomalyDetector.detect(currentMetrics, []);
      
      expect(anomalies.some(a => a.type === 'resource_exhaustion')).toBe(true);
    });
  });

  describe('AdaptationEngine', () => {
    beforeEach(async () => {
      await adaptationEngine.initialize();
    });

    it('should learn from successful healing', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-6',
        type: 'performance_degradation',
        severity: 'medium',
        description: 'Performance issue',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['service-1'],
        confidence: 0.8
      };

      const mockStrategy: HealingStrategy = {
        id: 'successful-strategy',
        name: 'Successful Strategy',
        description: 'Strategy that works',
        applicableAnomalies: ['performance_degradation'],
        actions: [],
        estimatedRecoveryTime: 60000,
        riskLevel: 0.3
      };

      const mockResult = {
        success: true,
        strategy: mockStrategy,
        anomaly: mockAnomaly,
        executedActions: [],
        startTime: new Date(),
        endTime: new Date(),
        rollbackPerformed: false
      };

      const adaptationRule = await adaptationEngine.learn(mockAnomaly, mockStrategy, mockResult);
      
      expect(adaptationRule).toBeDefined();
      expect(adaptationRule?.confidence).toBeGreaterThan(0);
      expect(adaptationRule?.successRate).toBe(1.0);
    });

    it('should learn from failed healing', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-7',
        type: 'high_error_rate',
        severity: 'high',
        description: 'High error rate',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['service-2'],
        confidence: 0.9
      };

      const mockStrategy: HealingStrategy = {
        id: 'failing-strategy',
        name: 'Failing Strategy',
        description: 'Strategy that fails',
        applicableAnomalies: ['high_error_rate'],
        actions: [],
        estimatedRecoveryTime: 60000,
        riskLevel: 0.4
      };

      const mockResult = {
        success: false,
        strategy: mockStrategy,
        anomaly: mockAnomaly,
        executedActions: [],
        startTime: new Date(),
        endTime: new Date(),
        error: 'Strategy failed',
        rollbackPerformed: true
      };

      const adaptationRule = await adaptationEngine.learn(mockAnomaly, mockStrategy, mockResult);
      
      expect(adaptationRule).toBeDefined();
      expect(adaptationRule?.action).toContain('avoid_strategy');
      expect(adaptationRule?.successRate).toBe(0.0);
    });
  });

  describe('HealingStrategies', () => {
    beforeEach(async () => {
      await healingStrategies.initialize();
    });

    it('should find applicable strategies for anomaly', async () => {
      const mockAnomaly: Anomaly = {
        id: 'test-anomaly-8',
        type: 'performance_degradation',
        severity: 'medium',
        description: 'Performance issue',
        metrics: {},
        timestamp: new Date(),
        affectedComponents: ['service-1'],
        confidence: 0.8
      };

      const strategies = await healingStrategies.findApplicableStrategies(mockAnomaly);
      
      expect(strategies.length).toBeGreaterThan(0);
      expect(strategies.every(s => s.applicableAnomalies.includes('performance_degradation'))).toBe(true);
    });

    it('should add custom strategy', async () => {
      const customStrategy: HealingStrategy = {
        id: 'custom-test-strategy',
        name: 'Custom Test Strategy',
        description: 'Custom strategy for testing',
        applicableAnomalies: ['test_anomaly'],
        actions: [],
        estimatedRecoveryTime: 30000,
        riskLevel: 0.2
      };

      await healingStrategies.addStrategy(customStrategy);
      
      const allStrategies = healingStrategies.getAllStrategies();
      expect(allStrategies.some(s => s.id === 'custom-test-strategy')).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete healing workflow', async () => {
      // Initialisation du système complet
      await immuneSystem.initialize();

      // Simulation d'une anomalie
      const mockAnomaly: Anomaly = {
        id: 'integration-test-anomaly',
        type: 'performance_degradation',
        severity: 'high',
        description: 'Integration test anomaly',
        metrics: { responseTime: 5000 },
        timestamp: new Date(),
        affectedComponents: ['test-service'],
        confidence: 0.9
      };

      // Mock des actions de service
      const restartSpy = jest.spyOn(serviceController, 'restartService');
      restartSpy.mockResolvedValue();

      // Déclenchement du processus de guérison
      const healingPromise = new Promise((resolve) => {
        immuneSystem.once('healing-successful', resolve);
      });

      // Simulation de la détection d'anomalie
      await immuneSystem.handleNewAnomaly(mockAnomaly);

      // Attente de la guérison
      await healingPromise;

      // Vérifications
      expect(restartSpy).toHaveBeenCalled();
      expect(mockMemory.store).toHaveBeenCalled(); // Sauvegarde des résultats
    });
  });
});
