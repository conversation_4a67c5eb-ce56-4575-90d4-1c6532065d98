import { CentralMemory } from '../memory/CentralMemory';
import { Anomaly, HealingStrategy } from './types';
export interface HealingStrategiesConfig {
    memory: CentralMemory;
    customStrategies?: HealingStrategy[];
}
/**
 * Gestionnaire de Stratégies de Guérison
 *
 * Contient et gère les stratégies prédéfinies et personnalisées
 * pour la résolution automatique des anomalies.
 */
export declare class HealingStrategies {
    private memory;
    private strategies;
    private isInitialized;
    constructor(config: HealingStrategiesConfig);
    /**
     * Initialise les stratégies de guérison
     */
    initialize(): Promise<void>;
    /**
     * Trouve les stratégies applicables pour une anomalie
     */
    findApplicableStrategies(anomaly: Anomaly): Promise<HealingStrategy[]>;
    /**
     * Ajoute une nouvelle stratégie
     */
    addStrategy(strategy: HealingStrategy): Promise<void>;
    /**
     * Met à jour une stratégie existante
     */
    updateStrategy(strategyId: string, updates: Partial<HealingStrategy>): Promise<void>;
    /**
     * Supprime une stratégie
     */
    removeStrategy(strategyId: string): Promise<void>;
    /**
     * Obtient le nombre total de stratégies
     */
    getStrategiesCount(): number;
    /**
     * Obtient toutes les stratégies
     */
    getAllStrategies(): HealingStrategy[];
    /**
     * Vérifie si une stratégie est applicable à une anomalie
     */
    private isStrategyApplicable;
    /**
     * Charge les stratégies par défaut
     */
    private loadDefaultStrategies;
    /**
     * Charge les stratégies personnalisées depuis la mémoire
     */
    private loadCustomStrategiesFromMemory;
    /**
     * Sauvegarde les stratégies personnalisées en mémoire
     */
    private saveCustomStrategiesToMemory;
}
//# sourceMappingURL=HealingStrategies.d.ts.map