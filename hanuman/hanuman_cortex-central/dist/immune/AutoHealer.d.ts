import { EventEmitter } from 'events';
import { Anomaly, HealingStrategy, HealingResult, AutoHealerConfig } from './types';
/**
 * Auto-Healer - Système de Réparation Automatique
 *
 * Responsable de l'application automatique de stratégies de guérison
 * pour résoudre les anomalies détectées dans le système.
 */
export declare class AutoHealer extends EventEmitter {
    private memory;
    private communication;
    private serviceController;
    private healingStrategies;
    private isInitialized;
    private maxConcurrentHealings;
    private defaultTimeout;
    private riskThreshold;
    private enableLearning;
    private enableRollback;
    private activeHealings;
    private healingHistory;
    private maxHistorySize;
    private stats;
    constructor(config: AutoHealerConfig);
    /**
     * Initialise l'Auto-Healer
     */
    initialize(): Promise<void>;
    /**
     * Sélectionne la meilleure stratégie de guérison pour une anomalie
     */
    selectStrategy(anomaly: Anomaly): Promise<HealingStrategy | null>;
    /**
     * Applique une stratégie de guérison
     */
    apply(strategy: HealingStrategy, anomaly: Anomaly): Promise<HealingResult>;
    /**
     * Construit le contexte de guérison
     */
    private buildHealingContext;
    /**
     * Utilitaire pour attendre
     */
    private sleep;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Évalue et classe les stratégies par pertinence
     */
    private evaluateStrategies;
    /**
     * Exécute une action de guérison
     */
    private executeAction;
    /**
     * Effectue un rollback des actions exécutées
     */
    private performRollback;
    /**
     * Calcule le score d'amélioration après guérison
     */
    private calculateImprovementScore;
    /**
     * Collecte les métriques actuelles du système
     */
    private collectCurrentMetrics;
    /**
     * Obtient les composants affectés par une anomalie
     */
    private getAffectedComponents;
    /**
     * Obtient les ressources disponibles
     */
    private getAvailableResources;
    /**
     * Obtient les contraintes système
     */
    private getSystemConstraints;
    /**
     * Obtient le taux de succès historique d'une stratégie
     */
    private getHistoricalSuccessRate;
    /**
     * Calcule le score de ressources pour une stratégie
     */
    private calculateResourceScore;
    /**
     * Envoie une notification
     */
    private sendNotification;
    /**
     * Met à jour les statistiques
     */
    private updateStats;
    /**
     * Charge l'historique des guérisons depuis la mémoire
     */
    private loadHealingHistory;
    /**
     * Stocke un résultat de guérison en mémoire
     */
    private storeHealingResult;
    /**
     * Obtient les statistiques de l'Auto-Healer
     */
    getStats(): {
        activeHealings: number;
        totalStrategies: number;
        totalHealings: number;
        successfulHealings: number;
        failedHealings: number;
        rolledBackHealings: number;
        averageHealingTime: number;
        lastHealing: Date | null;
    };
}
//# sourceMappingURL=AutoHealer.d.ts.map