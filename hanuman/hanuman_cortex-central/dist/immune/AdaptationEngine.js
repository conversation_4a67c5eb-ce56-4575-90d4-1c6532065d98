"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdaptationEngine = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
/**
 * Moteur d'Adaptation - Apprentissage et Évolution
 *
 * Responsable de l'apprentissage à partir des succès et échecs
 * de guérison pour améliorer les stratégies futures.
 */
class AdaptationEngine extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        this.adaptationRules = new Map();
        this.learningHistory = [];
        // Statistiques d'apprentissage
        this.learningStats = {
            totalLearningEvents: 0,
            rulesCreated: 0,
            rulesUpdated: 0,
            averageConfidence: 0,
            lastLearning: null
        };
        this.memory = config.memory;
        this.learningRate = config.learningRate || 0.1;
        this.confidenceThreshold = config.confidenceThreshold || 0.7;
        this.maxRules = config.maxRules || 1000;
        this.enableAutoCreation = config.enableAutoCreation !== false;
    }
    /**
     * Initialise le moteur d'adaptation
     */
    async initialize() {
        try {
            logger_1.logger.info('🧠 Initialisation du Moteur d\'Adaptation...');
            // Chargement des règles d'adaptation depuis la mémoire
            await this.loadAdaptationRules();
            // Chargement de l'historique d'apprentissage
            await this.loadLearningHistory();
            this.isInitialized = true;
            logger_1.logger.info(`✅ Moteur d'Adaptation initialisé avec ${this.adaptationRules.size} règles`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Moteur d\'Adaptation:', error);
            throw error;
        }
    }
    /**
     * Apprend d'un résultat de guérison
     */
    async learn(anomaly, strategy, result) {
        try {
            logger_1.logger.debug(`🎓 Apprentissage à partir du résultat: ${result.success ? 'succès' : 'échec'}`);
            // Enregistrement dans l'historique
            this.learningHistory.push({
                anomaly,
                strategy,
                result,
                timestamp: new Date()
            });
            // Limitation de l'historique
            if (this.learningHistory.length > 10000) {
                this.learningHistory.shift();
            }
            // Mise à jour des statistiques
            this.learningStats.totalLearningEvents++;
            this.learningStats.lastLearning = new Date();
            let adaptationRule = null;
            if (result.success && !result.rollbackPerformed) {
                // Apprentissage à partir d'un succès
                adaptationRule = await this.learnFromSuccess(anomaly, strategy, result);
            }
            else {
                // Apprentissage à partir d'un échec
                adaptationRule = await this.learnFromFailure(anomaly, strategy, result);
            }
            // Sauvegarde en mémoire
            await this.saveAdaptationRules();
            await this.saveLearningHistory();
            return adaptationRule;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'apprentissage:', error);
            return null;
        }
    }
    /**
     * Applique une règle d'adaptation
     */
    async apply(adaptationRule) {
        try {
            logger_1.logger.info(`🔧 Application de la règle d'adaptation: ${adaptationRule.name}`);
            // Mise à jour de la dernière utilisation
            adaptationRule.lastUsed = new Date();
            this.adaptationRules.set(adaptationRule.id, adaptationRule);
            // Émission d'événement pour notification
            this.emit('adaptation-applied', {
                rule: adaptationRule,
                timestamp: new Date()
            });
            // Sauvegarde
            await this.saveAdaptationRules();
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'application de l\'adaptation:', error);
            throw error;
        }
    }
    /**
     * Obtient les règles d'adaptation applicables à une situation
     */
    getApplicableRules(anomaly) {
        const applicableRules = [];
        for (const rule of this.adaptationRules.values()) {
            if (this.isRuleApplicable(rule, anomaly)) {
                applicableRules.push(rule);
            }
        }
        // Tri par confiance décroissante
        applicableRules.sort((a, b) => b.confidence - a.confidence);
        return applicableRules.filter(rule => rule.confidence >= this.confidenceThreshold);
    }
    /**
     * Obtient les statistiques d'apprentissage
     */
    getStats() {
        return {
            ...this.learningStats,
            totalRules: this.adaptationRules.size,
            highConfidenceRules: Array.from(this.adaptationRules.values())
                .filter(rule => rule.confidence >= this.confidenceThreshold).length
        };
    }
    /**
     * Apprend à partir d'un succès
     */
    async learnFromSuccess(anomaly, strategy, result) {
        const ruleId = `success_${anomaly.type}_${strategy.id}`;
        let rule = this.adaptationRules.get(ruleId);
        if (rule) {
            // Mise à jour d'une règle existante
            rule.successRate = this.updateSuccessRate(rule.successRate, true);
            rule.confidence = Math.min(1.0, rule.confidence + this.learningRate);
            this.learningStats.rulesUpdated++;
        }
        else if (this.enableAutoCreation && this.adaptationRules.size < this.maxRules) {
            // Création d'une nouvelle règle
            rule = {
                id: ruleId,
                name: `Succès ${strategy.name} pour ${anomaly.type}`,
                description: `Stratégie ${strategy.name} efficace pour les anomalies de type ${anomaly.type}`,
                condition: `anomaly.type === '${anomaly.type}' && anomaly.severity === '${anomaly.severity}'`,
                action: `prefer_strategy('${strategy.id}')`,
                confidence: this.learningRate,
                successRate: 1.0,
                lastUsed: new Date(),
                createdFrom: { anomaly, strategy, result }
            };
            this.adaptationRules.set(ruleId, rule);
            this.learningStats.rulesCreated++;
        }
        if (rule) {
            this.updateAverageConfidence();
            logger_1.logger.debug(`📈 Règle mise à jour: ${rule.name} (confiance: ${rule.confidence.toFixed(2)})`);
        }
        return rule;
    }
    /**
     * Apprend à partir d'un échec
     */
    async learnFromFailure(anomaly, strategy, result) {
        const ruleId = `failure_${anomaly.type}_${strategy.id}`;
        let rule = this.adaptationRules.get(ruleId);
        if (rule) {
            // Mise à jour d'une règle existante
            rule.successRate = this.updateSuccessRate(rule.successRate, false);
            rule.confidence = Math.max(0.0, rule.confidence - this.learningRate);
            this.learningStats.rulesUpdated++;
        }
        else if (this.enableAutoCreation && this.adaptationRules.size < this.maxRules) {
            // Création d'une nouvelle règle d'évitement
            rule = {
                id: ruleId,
                name: `Éviter ${strategy.name} pour ${anomaly.type}`,
                description: `Stratégie ${strategy.name} inefficace pour les anomalies de type ${anomaly.type}`,
                condition: `anomaly.type === '${anomaly.type}' && anomaly.severity === '${anomaly.severity}'`,
                action: `avoid_strategy('${strategy.id}')`,
                confidence: this.learningRate,
                successRate: 0.0,
                lastUsed: new Date(),
                createdFrom: { anomaly, strategy, result }
            };
            this.adaptationRules.set(ruleId, rule);
            this.learningStats.rulesCreated++;
        }
        // Recherche de règles de succès contradictoires à ajuster
        const successRuleId = `success_${anomaly.type}_${strategy.id}`;
        const successRule = this.adaptationRules.get(successRuleId);
        if (successRule) {
            successRule.successRate = this.updateSuccessRate(successRule.successRate, false);
            successRule.confidence = Math.max(0.0, successRule.confidence - this.learningRate * 0.5);
        }
        if (rule) {
            this.updateAverageConfidence();
            logger_1.logger.debug(`📉 Règle d'évitement créée/mise à jour: ${rule.name} (confiance: ${rule.confidence.toFixed(2)})`);
        }
        return rule;
    }
    /**
     * Met à jour le taux de succès avec une moyenne mobile
     */
    updateSuccessRate(currentRate, success) {
        const newValue = success ? 1.0 : 0.0;
        return currentRate * (1 - this.learningRate) + newValue * this.learningRate;
    }
    /**
     * Vérifie si une règle est applicable à une anomalie
     */
    isRuleApplicable(rule, anomaly) {
        try {
            // Évaluation simplifiée de la condition
            // Dans une implémentation complète, on utiliserait un moteur d'évaluation plus sophistiqué
            const condition = rule.condition
                .replace(/anomaly\.type/g, `'${anomaly.type}'`)
                .replace(/anomaly\.severity/g, `'${anomaly.severity}'`);
            // Évaluation basique (à améliorer pour la production)
            if (condition.includes(`'${anomaly.type}'`) && condition.includes(`'${anomaly.severity}'`)) {
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.warn(`⚠️ Erreur lors de l'évaluation de la règle ${rule.id}:`, error);
            return false;
        }
    }
    /**
     * Met à jour la confiance moyenne
     */
    updateAverageConfidence() {
        const rules = Array.from(this.adaptationRules.values());
        if (rules.length > 0) {
            const totalConfidence = rules.reduce((sum, rule) => sum + rule.confidence, 0);
            this.learningStats.averageConfidence = totalConfidence / rules.length;
        }
    }
    /**
     * Charge les règles d'adaptation depuis la mémoire
     */
    async loadAdaptationRules() {
        try {
            const rules = await this.memory.retrieve('adaptation_rules');
            if (rules && Array.isArray(rules)) {
                rules.forEach((rule) => {
                    this.adaptationRules.set(rule.id, rule);
                });
                logger_1.logger.info(`📖 ${rules.length} règles d'adaptation chargées depuis la mémoire`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger les règles d\'adaptation:', error);
        }
    }
    /**
     * Sauvegarde les règles d'adaptation en mémoire
     */
    async saveAdaptationRules() {
        try {
            const rules = Array.from(this.adaptationRules.values());
            await this.memory.store('adaptation_rules', rules);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde des règles d\'adaptation:', error);
        }
    }
    /**
     * Charge l'historique d'apprentissage depuis la mémoire
     */
    async loadLearningHistory() {
        try {
            const history = await this.memory.retrieve('learning_history');
            if (history && Array.isArray(history)) {
                this.learningHistory = history.slice(-1000); // Garde seulement les 1000 derniers
                logger_1.logger.info(`📚 Historique d'apprentissage chargé: ${this.learningHistory.length} entrées`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger l\'historique d\'apprentissage:', error);
        }
    }
    /**
     * Sauvegarde l'historique d'apprentissage en mémoire
     */
    async saveLearningHistory() {
        try {
            // Garde seulement les 1000 dernières entrées pour éviter une croissance excessive
            const recentHistory = this.learningHistory.slice(-1000);
            await this.memory.store('learning_history', recentHistory);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde de l\'historique d\'apprentissage:', error);
        }
    }
}
exports.AdaptationEngine = AdaptationEngine;
//# sourceMappingURL=AdaptationEngine.js.map