import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { HealthMetrics, Anomaly } from './types';
export interface AnomalyDetectorConfig {
    memory: CentralMemory;
    sensitivityLevel?: number;
    windowSize?: number;
    thresholds?: AnomalyThresholds;
}
export interface AnomalyThresholds {
    systemLoad: {
        warning: number;
        critical: number;
    };
    memoryUsage: {
        warning: number;
        critical: number;
    };
    responseTime: {
        warning: number;
        critical: number;
    };
    errorRate: {
        warning: number;
        critical: number;
    };
    agentHealth: {
        warning: number;
        critical: number;
    };
    synapticHealth: {
        warning: number;
        critical: number;
    };
}
export interface DetectionRule {
    id: string;
    name: string;
    type: 'threshold' | 'trend' | 'pattern' | 'statistical';
    condition: (metrics: HealthMetrics, history: HealthMetrics[]) => boolean;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    confidence: number;
}
/**
 * Détecteur d'Anomalies - Surveillance Intelligente du Système
 *
 * Utilise des algorithmes de détection d'anomalies pour identifier
 * les comportements anormaux du système en temps réel.
 */
export declare class AnomalyDetector extends EventEmitter {
    private memory;
    private sensitivityLevel;
    private windowSize;
    private thresholds;
    private isInitialized;
    private detectionRules;
    private baselineMetrics;
    private standardDeviations;
    private detectionHistory;
    private detectionStats;
    constructor(config: AnomalyDetectorConfig);
    /**
     * Initialise le détecteur d'anomalies
     */
    initialize(): Promise<void>;
    /**
     * Détecte les anomalies dans les métriques actuelles
     */
    detect(currentMetrics: HealthMetrics, metricsHistory: HealthMetrics[]): Promise<Anomaly[]>;
    /**
     * Initialise les règles de détection par défaut
     */
    private initializeDetectionRules;
    /**
     * Détection statistique avancée
     */
    private detectStatisticalAnomalies;
    /**
     * Détection de patterns anormaux
     */
    private detectPatternAnomalies;
    /**
     * Détecte les anomalies de cycle
     */
    private detectCycleAnomaly;
    /**
     * Détecte les anomalies de corrélation
     */
    private detectCorrelationAnomaly;
    /**
     * Calcule la corrélation entre deux séries
     */
    private calculateCorrelation;
    /**
     * Crée une anomalie à partir d'une règle
     */
    private createAnomalyFromRule;
    /**
     * Mappe le type de règle au type d'anomalie
     */
    private mapRuleTypeToAnomalyType;
    /**
     * Extrait les métriques pertinentes pour une règle
     */
    private extractRelevantMetrics;
    /**
     * Identifie les composants affectés
     */
    private identifyAffectedComponents;
    /**
     * Filtre et déduplique les anomalies
     */
    private filterAndDeduplicateAnomalies;
    /**
     * Met à jour les modèles statistiques
     */
    private updateStatisticalModels;
    /**
     * Récupère la valeur d'une métrique
     */
    private getMetricValue;
    /**
     * Stocke les détections pour apprentissage
     */
    private storeDetections;
    /**
     * Met à jour les statistiques de détection
     */
    private updateDetectionStats;
    /**
     * Charge les modèles depuis la mémoire
     */
    private loadModelsFromMemory;
    /**
     * Charge les règles personnalisées
     */
    private loadCustomRules;
    /**
     * Récupère les seuils par défaut
     */
    private getDefaultThresholds;
    /**
     * Récupère le statut du détecteur
     */
    getStatus(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
    /**
     * Sauvegarde les modèles en mémoire
     */
    private saveModelsToMemory;
}
//# sourceMappingURL=AnomalyDetector.d.ts.map