"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealingStrategies = void 0;
const logger_1 = require("../utils/logger");
/**
 * Gestionnaire de Stratégies de Guérison
 *
 * Contient et gère les stratégies prédéfinies et personnalisées
 * pour la résolution automatique des anomalies.
 */
class HealingStrategies {
    constructor(config) {
        this.strategies = new Map();
        this.isInitialized = false;
        this.memory = config.memory;
        // Chargement des stratégies personnalisées
        if (config.customStrategies) {
            config.customStrategies.forEach(strategy => {
                this.strategies.set(strategy.id, strategy);
            });
        }
    }
    /**
     * Initialise les stratégies de guérison
     */
    async initialize() {
        try {
            logger_1.logger.info('🎯 Initialisation des Stratégies de Guérison...');
            // Chargement des stratégies par défaut
            this.loadDefaultStrategies();
            // Chargement des stratégies personnalisées depuis la mémoire
            await this.loadCustomStrategiesFromMemory();
            this.isInitialized = true;
            logger_1.logger.info(`✅ ${this.strategies.size} stratégies de guérison chargées`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation des stratégies:', error);
            throw error;
        }
    }
    /**
     * Trouve les stratégies applicables pour une anomalie
     */
    async findApplicableStrategies(anomaly) {
        const applicableStrategies = [];
        for (const strategy of this.strategies.values()) {
            if (this.isStrategyApplicable(strategy, anomaly)) {
                applicableStrategies.push(strategy);
            }
        }
        // Tri par niveau de risque (moins risqué en premier)
        applicableStrategies.sort((a, b) => a.riskLevel - b.riskLevel);
        logger_1.logger.debug(`${applicableStrategies.length} stratégies trouvées pour l'anomalie ${anomaly.type}`);
        return applicableStrategies;
    }
    /**
     * Ajoute une nouvelle stratégie
     */
    async addStrategy(strategy) {
        this.strategies.set(strategy.id, strategy);
        // Sauvegarde en mémoire
        await this.saveCustomStrategiesToMemory();
        logger_1.logger.info(`➕ Nouvelle stratégie ajoutée: ${strategy.name}`);
    }
    /**
     * Met à jour une stratégie existante
     */
    async updateStrategy(strategyId, updates) {
        const existingStrategy = this.strategies.get(strategyId);
        if (!existingStrategy) {
            throw new Error(`Stratégie non trouvée: ${strategyId}`);
        }
        const updatedStrategy = { ...existingStrategy, ...updates };
        this.strategies.set(strategyId, updatedStrategy);
        // Sauvegarde en mémoire
        await this.saveCustomStrategiesToMemory();
        logger_1.logger.info(`🔄 Stratégie mise à jour: ${updatedStrategy.name}`);
    }
    /**
     * Supprime une stratégie
     */
    async removeStrategy(strategyId) {
        if (this.strategies.delete(strategyId)) {
            await this.saveCustomStrategiesToMemory();
            logger_1.logger.info(`🗑️ Stratégie supprimée: ${strategyId}`);
        }
    }
    /**
     * Obtient le nombre total de stratégies
     */
    getStrategiesCount() {
        return this.strategies.size;
    }
    /**
     * Obtient toutes les stratégies
     */
    getAllStrategies() {
        return Array.from(this.strategies.values());
    }
    /**
     * Vérifie si une stratégie est applicable à une anomalie
     */
    isStrategyApplicable(strategy, anomaly) {
        // Vérification du type d'anomalie
        if (!strategy.applicableAnomalies.includes(anomaly.type)) {
            return false;
        }
        // Vérification de la sévérité
        const severityLevels = ['low', 'medium', 'high', 'critical'];
        const anomalySeverityIndex = severityLevels.indexOf(anomaly.severity);
        // Certaines stratégies ne sont applicables qu'à partir d'un certain niveau de sévérité
        if (strategy.id.includes('critical') && anomalySeverityIndex < 3) {
            return false;
        }
        if (strategy.id.includes('emergency') && anomalySeverityIndex < 2) {
            return false;
        }
        return true;
    }
    /**
     * Charge les stratégies par défaut
     */
    loadDefaultStrategies() {
        // Stratégie de redémarrage simple
        this.strategies.set('restart-simple', {
            id: 'restart-simple',
            name: 'Redémarrage Simple',
            description: 'Redémarre le service affecté',
            applicableAnomalies: ['performance_degradation', 'high_error_rate', 'agent_failure'],
            actions: [{
                    type: 'restart',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: {},
                    timeout: 300000
                }],
            estimatedRecoveryTime: 60000, // 1 minute
            riskLevel: 0.3,
            maxRetries: 2
        });
        // Stratégie de mise à l'échelle
        this.strategies.set('scale-up', {
            id: 'scale-up',
            name: 'Augmentation d\'Échelle',
            description: 'Augmente le nombre de répliques du service',
            applicableAnomalies: ['performance_degradation', 'resource_exhaustion'],
            actions: [{
                    type: 'scale',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { replicas: '${current_replicas + 2}' },
                    timeout: 180000
                }],
            estimatedRecoveryTime: 120000, // 2 minutes
            riskLevel: 0.4,
            rollbackActions: [{
                    type: 'scale',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { replicas: '${original_replicas}' },
                    timeout: 180000
                }]
        });
        // Stratégie de redirection de trafic
        this.strategies.set('traffic-redirect', {
            id: 'traffic-redirect',
            name: 'Redirection de Trafic',
            description: 'Redirige le trafic vers un service de secours',
            applicableAnomalies: ['service_unavailable', 'high_error_rate'],
            actions: [{
                    type: 'redirect',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { destination: '${backup_service}' },
                    timeout: 60000
                }],
            estimatedRecoveryTime: 30000, // 30 secondes
            riskLevel: 0.5,
            prerequisites: ['backup_service_available']
        });
        // Stratégie d'optimisation des ressources
        this.strategies.set('resource-optimization', {
            id: 'resource-optimization',
            name: 'Optimisation des Ressources',
            description: 'Optimise l\'allocation des ressources',
            applicableAnomalies: ['resource_exhaustion', 'performance_degradation'],
            actions: [
                {
                    type: 'optimize',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: {
                        clearCache: true,
                        adjustResources: {
                            cpu: '${current_cpu * 1.5}',
                            memory: '${current_memory * 1.2}'
                        }
                    },
                    timeout: 120000
                }
            ],
            estimatedRecoveryTime: 90000, // 1.5 minutes
            riskLevel: 0.2
        });
        // Stratégie d'isolation de composant
        this.strategies.set('component-isolation', {
            id: 'component-isolation',
            name: 'Isolation de Composant',
            description: 'Isole le composant défaillant pour éviter la propagation',
            applicableAnomalies: ['security_breach', 'data_corruption', 'communication_failure'],
            actions: [{
                    type: 'isolate',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: {},
                    timeout: 60000
                }, {
                    type: 'notify',
                    target: 'operators',
                    parameters: {
                        message: 'Composant ${anomaly.affectedComponents[0]} isolé suite à ${anomaly.type}',
                        severity: 'high'
                    },
                    timeout: 5000
                }],
            estimatedRecoveryTime: 60000, // 1 minute
            riskLevel: 0.8 // Risque élevé car peut affecter la disponibilité
        });
        // Stratégie de rollback d'urgence
        this.strategies.set('emergency-rollback', {
            id: 'emergency-rollback',
            name: 'Rollback d\'Urgence',
            description: 'Effectue un rollback vers la version précédente',
            applicableAnomalies: ['high_error_rate', 'service_unavailable', 'data_corruption'],
            actions: [{
                    type: 'rollback',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { version: 'previous' },
                    timeout: 300000
                }],
            estimatedRecoveryTime: 180000, // 3 minutes
            riskLevel: 0.6,
            successCriteria: ['error_rate < 1%', 'response_time < 500ms']
        });
        // Stratégie de redémarrage en cascade
        this.strategies.set('cascade-restart', {
            id: 'cascade-restart',
            name: 'Redémarrage en Cascade',
            description: 'Redémarre le service et ses dépendances',
            applicableAnomalies: ['communication_failure', 'agent_failure'],
            actions: [
                {
                    type: 'restart',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: {},
                    timeout: 300000,
                    priority: 1
                },
                {
                    type: 'restart',
                    target: '${dependencies}',
                    parameters: { delay: 30000 }, // Attendre 30s avant de redémarrer les dépendances
                    timeout: 300000,
                    priority: 2
                }
            ],
            estimatedRecoveryTime: 240000, // 4 minutes
            riskLevel: 0.7,
            maxRetries: 1
        });
        // Stratégie de nettoyage et redémarrage
        this.strategies.set('clean-restart', {
            id: 'clean-restart',
            name: 'Nettoyage et Redémarrage',
            description: 'Vide les caches et redémarre le service',
            applicableAnomalies: ['performance_degradation', 'resource_exhaustion'],
            actions: [
                {
                    type: 'optimize',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { clearCache: true },
                    timeout: 60000
                },
                {
                    type: 'restart',
                    target: '${anomaly.affectedComponents[0]}',
                    parameters: { delay: 10000 },
                    timeout: 300000
                }
            ],
            estimatedRecoveryTime: 120000, // 2 minutes
            riskLevel: 0.3
        });
        logger_1.logger.info(`📚 ${this.strategies.size} stratégies par défaut chargées`);
    }
    /**
     * Charge les stratégies personnalisées depuis la mémoire
     */
    async loadCustomStrategiesFromMemory() {
        try {
            const customStrategies = await this.memory.retrieve('custom_healing_strategies');
            if (customStrategies && Array.isArray(customStrategies)) {
                customStrategies.forEach((strategy) => {
                    this.strategies.set(strategy.id, strategy);
                });
                logger_1.logger.info(`📖 ${customStrategies.length} stratégies personnalisées chargées depuis la mémoire`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger les stratégies personnalisées:', error);
        }
    }
    /**
     * Sauvegarde les stratégies personnalisées en mémoire
     */
    async saveCustomStrategiesToMemory() {
        try {
            const customStrategies = Array.from(this.strategies.values())
                .filter(strategy => !strategy.id.startsWith('default-'));
            await this.memory.store('custom_healing_strategies', customStrategies);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde des stratégies:', error);
        }
    }
}
exports.HealingStrategies = HealingStrategies;
//# sourceMappingURL=HealingStrategies.js.map