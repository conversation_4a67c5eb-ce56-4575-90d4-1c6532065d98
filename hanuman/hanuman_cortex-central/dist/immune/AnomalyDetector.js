"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnomalyDetector = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
/**
 * Détecteur d'Anomalies - Surveillance Intelligente du Système
 *
 * Utilise des algorithmes de détection d'anomalies pour identifier
 * les comportements anormaux du système en temps réel.
 */
class AnomalyDetector extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        // Règles de détection
        this.detectionRules = new Map();
        // Modèles statistiques
        this.baselineMetrics = new Map();
        this.standardDeviations = new Map();
        // Historique des détections
        this.detectionHistory = [];
        // Statistiques de détection
        this.detectionStats = {
            totalDetections: 0,
            falsePositives: 0,
            truePositives: 0,
            averageConfidence: 0,
            detectionRate: 0
        };
        this.memory = config.memory;
        this.sensitivityLevel = config.sensitivityLevel || 0.7;
        this.windowSize = config.windowSize || 50;
        this.thresholds = config.thresholds || this.getDefaultThresholds();
        this.initializeDetectionRules();
    }
    /**
     * Initialise le détecteur d'anomalies
     */
    async initialize() {
        try {
            logger_1.logger.info('🔍 Initialisation du Détecteur d\'Anomalies...');
            // Chargement des modèles depuis la mémoire
            await this.loadModelsFromMemory();
            // Chargement des règles personnalisées
            await this.loadCustomRules();
            this.isInitialized = true;
            logger_1.logger.info('✅ Détecteur d\'Anomalies initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Détecteur d\'Anomalies:', error);
            throw error;
        }
    }
    /**
     * Détecte les anomalies dans les métriques actuelles
     */
    async detect(currentMetrics, metricsHistory) {
        if (!this.isInitialized) {
            throw new Error('Détecteur d\'anomalies non initialisé');
        }
        const anomalies = [];
        const detectionStartTime = Date.now();
        try {
            // Mise à jour des modèles statistiques
            this.updateStatisticalModels(currentMetrics, metricsHistory);
            // Application des règles de détection
            for (const [ruleId, rule] of this.detectionRules) {
                try {
                    if (rule.condition(currentMetrics, metricsHistory)) {
                        const anomaly = this.createAnomalyFromRule(rule, currentMetrics);
                        anomalies.push(anomaly);
                        logger_1.logger.debug(`🚨 Anomalie détectée par la règle ${rule.name}: ${anomaly.description}`);
                    }
                }
                catch (error) {
                    logger_1.logger.error(`❌ Erreur lors de l'application de la règle ${rule.name}:`, error);
                }
            }
            // Détection statistique avancée
            const statisticalAnomalies = await this.detectStatisticalAnomalies(currentMetrics, metricsHistory);
            anomalies.push(...statisticalAnomalies);
            // Détection de patterns
            const patternAnomalies = await this.detectPatternAnomalies(currentMetrics, metricsHistory);
            anomalies.push(...patternAnomalies);
            // Filtrage et déduplication
            const filteredAnomalies = this.filterAndDeduplicateAnomalies(anomalies);
            // Mise à jour des statistiques
            this.updateDetectionStats(filteredAnomalies, Date.now() - detectionStartTime);
            // Stockage des détections pour apprentissage
            if (filteredAnomalies.length > 0) {
                await this.storeDetections(filteredAnomalies, currentMetrics);
            }
            return filteredAnomalies;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la détection d\'anomalies:', error);
            return [];
        }
    }
    /**
     * Initialise les règles de détection par défaut
     */
    initializeDetectionRules() {
        // Règle de charge système élevée
        this.detectionRules.set('high-system-load', {
            id: 'high-system-load',
            name: 'Charge Système Élevée',
            type: 'threshold',
            condition: (metrics) => metrics.systemLoad > this.thresholds.systemLoad.warning,
            severity: 'medium',
            description: 'Charge système au-dessus du seuil normal',
            confidence: 0.8
        });
        // Règle de mémoire critique
        this.detectionRules.set('critical-memory', {
            id: 'critical-memory',
            name: 'Mémoire Critique',
            type: 'threshold',
            condition: (metrics) => metrics.memoryUsage > this.thresholds.memoryUsage.critical,
            severity: 'critical',
            description: 'Utilisation mémoire critique',
            confidence: 0.95
        });
        // Règle de temps de réponse élevé
        this.detectionRules.set('high-response-time', {
            id: 'high-response-time',
            name: 'Temps de Réponse Élevé',
            type: 'threshold',
            condition: (metrics) => metrics.responseTime > this.thresholds.responseTime.warning,
            severity: 'medium',
            description: 'Temps de réponse au-dessus de la normale',
            confidence: 0.7
        });
        // Règle de taux d'erreur élevé
        this.detectionRules.set('high-error-rate', {
            id: 'high-error-rate',
            name: 'Taux d\'Erreur Élevé',
            type: 'threshold',
            condition: (metrics) => metrics.errorRate > this.thresholds.errorRate.warning,
            severity: 'high',
            description: 'Taux d\'erreur anormalement élevé',
            confidence: 0.9
        });
        // Règle de santé d'agent faible
        this.detectionRules.set('agent-health-low', {
            id: 'agent-health-low',
            name: 'Santé d\'Agent Faible',
            type: 'threshold',
            condition: (metrics) => {
                for (const [agentId, health] of metrics.agentHealth) {
                    if (health < this.thresholds.agentHealth.warning) {
                        return true;
                    }
                }
                return false;
            },
            severity: 'medium',
            description: 'Un ou plusieurs agents en mauvaise santé',
            confidence: 0.8
        });
        // Règle de tendance dégradante
        this.detectionRules.set('degrading-trend', {
            id: 'degrading-trend',
            name: 'Tendance Dégradante',
            type: 'trend',
            condition: (metrics, history) => {
                if (history.length < 10)
                    return false;
                const recent = history.slice(-5);
                const older = history.slice(-10, -5);
                const recentAvgResponse = recent.reduce((sum, m) => sum + m.responseTime, 0) / recent.length;
                const olderAvgResponse = older.reduce((sum, m) => sum + m.responseTime, 0) / older.length;
                return (recentAvgResponse - olderAvgResponse) / olderAvgResponse > 0.2; // 20% de dégradation
            },
            severity: 'medium',
            description: 'Dégradation progressive des performances',
            confidence: 0.6
        });
        // Règle de pic soudain
        this.detectionRules.set('sudden-spike', {
            id: 'sudden-spike',
            name: 'Pic Soudain',
            type: 'statistical',
            condition: (metrics, history) => {
                if (history.length < 5)
                    return false;
                const recentAvg = history.slice(-5).reduce((sum, m) => sum + m.systemLoad, 0) / 5;
                const currentLoad = metrics.systemLoad;
                return currentLoad > recentAvg * 2; // Pic de plus de 200%
            },
            severity: 'high',
            description: 'Pic soudain de charge système',
            confidence: 0.85
        });
    }
    /**
     * Détection statistique avancée
     */
    async detectStatisticalAnomalies(currentMetrics, history) {
        const anomalies = [];
        if (history.length < 10)
            return anomalies; // Pas assez de données
        // Détection par écart-type
        const metrics = ['systemLoad', 'memoryUsage', 'responseTime', 'errorRate'];
        for (const metric of metrics) {
            const values = history.map(h => this.getMetricValue(h, metric));
            const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
            const stdDev = Math.sqrt(values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length);
            const currentValue = this.getMetricValue(currentMetrics, metric);
            const zScore = Math.abs((currentValue - mean) / stdDev);
            // Détection d'outlier (z-score > 2.5)
            if (zScore > 2.5) {
                anomalies.push({
                    id: `statistical-${metric}-${Date.now()}`,
                    type: 'performance',
                    severity: zScore > 3.5 ? 'high' : 'medium',
                    description: `Valeur statistiquement anormale pour ${metric}`,
                    metrics: { metric, value: currentValue, zScore, mean, stdDev },
                    timestamp: new Date(),
                    affectedComponents: [metric],
                    confidence: Math.min(0.95, zScore / 4) // Confiance basée sur le z-score
                });
            }
        }
        return anomalies;
    }
    /**
     * Détection de patterns anormaux
     */
    async detectPatternAnomalies(currentMetrics, history) {
        const anomalies = [];
        if (history.length < 20)
            return anomalies;
        // Détection de cycles anormaux
        const cycleAnomaly = this.detectCycleAnomaly(history);
        if (cycleAnomaly) {
            anomalies.push(cycleAnomaly);
        }
        // Détection de corrélations anormales
        const correlationAnomaly = this.detectCorrelationAnomaly(currentMetrics, history);
        if (correlationAnomaly) {
            anomalies.push(correlationAnomaly);
        }
        return anomalies;
    }
    /**
     * Détecte les anomalies de cycle
     */
    detectCycleAnomaly(history) {
        // Analyse simplifiée des cycles de charge
        const loads = history.map(h => h.systemLoad);
        const windowSize = 10;
        if (loads.length < windowSize * 2)
            return null;
        // Calcul de la variance dans différentes fenêtres
        const variances = [];
        for (let i = 0; i <= loads.length - windowSize; i++) {
            const window = loads.slice(i, i + windowSize);
            const mean = window.reduce((sum, v) => sum + v, 0) / window.length;
            const variance = window.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / window.length;
            variances.push(variance);
        }
        const avgVariance = variances.reduce((sum, v) => sum + v, 0) / variances.length;
        const currentVariance = variances[variances.length - 1];
        // Détection d'une variance anormalement élevée
        if (currentVariance > avgVariance * 3) {
            return {
                id: `cycle-anomaly-${Date.now()}`,
                type: 'performance',
                severity: 'medium',
                description: 'Pattern de charge anormalement variable',
                metrics: { currentVariance, avgVariance, ratio: currentVariance / avgVariance },
                timestamp: new Date(),
                affectedComponents: ['system-load'],
                confidence: 0.7
            };
        }
        return null;
    }
    /**
     * Détecte les anomalies de corrélation
     */
    detectCorrelationAnomaly(currentMetrics, history) {
        // Vérification de la corrélation normale entre charge et temps de réponse
        if (history.length < 15)
            return null;
        const loads = history.map(h => h.systemLoad);
        const responseTimes = history.map(h => h.responseTime);
        // Calcul de corrélation simple
        const correlation = this.calculateCorrelation(loads, responseTimes);
        // Normalement, charge et temps de réponse sont corrélés positivement
        if (correlation < 0.3 && currentMetrics.systemLoad > 0.6 && currentMetrics.responseTime < 100) {
            return {
                id: `correlation-anomaly-${Date.now()}`,
                type: 'performance',
                severity: 'medium',
                description: 'Corrélation anormale entre charge et temps de réponse',
                metrics: { correlation, systemLoad: currentMetrics.systemLoad, responseTime: currentMetrics.responseTime },
                timestamp: new Date(),
                affectedComponents: ['system-load', 'response-time'],
                confidence: 0.6
            };
        }
        return null;
    }
    /**
     * Calcule la corrélation entre deux séries
     */
    calculateCorrelation(x, y) {
        if (x.length !== y.length || x.length === 0)
            return 0;
        const n = x.length;
        const meanX = x.reduce((sum, v) => sum + v, 0) / n;
        const meanY = y.reduce((sum, v) => sum + v, 0) / n;
        let numerator = 0;
        let sumXSquared = 0;
        let sumYSquared = 0;
        for (let i = 0; i < n; i++) {
            const deltaX = x[i] - meanX;
            const deltaY = y[i] - meanY;
            numerator += deltaX * deltaY;
            sumXSquared += deltaX * deltaX;
            sumYSquared += deltaY * deltaY;
        }
        const denominator = Math.sqrt(sumXSquared * sumYSquared);
        return denominator === 0 ? 0 : numerator / denominator;
    }
    /**
     * Crée une anomalie à partir d'une règle
     */
    createAnomalyFromRule(rule, metrics) {
        return {
            id: `${rule.id}-${Date.now()}`,
            type: this.mapRuleTypeToAnomalyType(rule.type),
            severity: rule.severity,
            description: rule.description,
            metrics: this.extractRelevantMetrics(rule, metrics),
            timestamp: new Date(),
            affectedComponents: this.identifyAffectedComponents(rule, metrics),
            confidence: rule.confidence * this.sensitivityLevel
        };
    }
    /**
     * Mappe le type de règle au type d'anomalie
     */
    mapRuleTypeToAnomalyType(ruleType) {
        switch (ruleType) {
            case 'threshold':
            case 'statistical':
                return 'performance';
            case 'trend':
                return 'performance';
            case 'pattern':
                return 'communication';
            default:
                return 'performance';
        }
    }
    /**
     * Extrait les métriques pertinentes pour une règle
     */
    extractRelevantMetrics(rule, metrics) {
        // Extraction simplifiée - dans un vrai système, plus sophistiqué
        return {
            systemLoad: metrics.systemLoad,
            memoryUsage: metrics.memoryUsage,
            responseTime: metrics.responseTime,
            errorRate: metrics.errorRate,
            timestamp: metrics.timestamp
        };
    }
    /**
     * Identifie les composants affectés
     */
    identifyAffectedComponents(rule, metrics) {
        const components = [];
        if (rule.id.includes('system-load'))
            components.push('system');
        if (rule.id.includes('memory'))
            components.push('memory');
        if (rule.id.includes('response-time'))
            components.push('network');
        if (rule.id.includes('error-rate'))
            components.push('application');
        if (rule.id.includes('agent')) {
            // Identification des agents spécifiques en mauvaise santé
            for (const [agentId, health] of metrics.agentHealth) {
                if (health < this.thresholds.agentHealth.warning) {
                    components.push(`agent-${agentId}`);
                }
            }
        }
        return components.length > 0 ? components : ['system'];
    }
    /**
     * Filtre et déduplique les anomalies
     */
    filterAndDeduplicateAnomalies(anomalies) {
        // Suppression des doublons basée sur le type et la description
        const seen = new Set();
        const filtered = [];
        for (const anomaly of anomalies) {
            const key = `${anomaly.type}-${anomaly.description}`;
            if (!seen.has(key)) {
                seen.add(key);
                filtered.push(anomaly);
            }
        }
        // Filtrage par niveau de confiance
        return filtered.filter(anomaly => anomaly.confidence >= this.sensitivityLevel * 0.5);
    }
    /**
     * Met à jour les modèles statistiques
     */
    updateStatisticalModels(currentMetrics, history) {
        if (history.length < 5)
            return;
        const metrics = ['systemLoad', 'memoryUsage', 'responseTime', 'errorRate'];
        for (const metric of metrics) {
            const values = history.map(h => this.getMetricValue(h, metric));
            const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
            const stdDev = Math.sqrt(values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length);
            this.baselineMetrics.set(metric, mean);
            this.standardDeviations.set(metric, stdDev);
        }
    }
    /**
     * Récupère la valeur d'une métrique
     */
    getMetricValue(metrics, metricName) {
        switch (metricName) {
            case 'systemLoad': return metrics.systemLoad;
            case 'memoryUsage': return metrics.memoryUsage;
            case 'responseTime': return metrics.responseTime;
            case 'errorRate': return metrics.errorRate;
            case 'synapticHealth': return metrics.synapticHealth;
            default: return 0;
        }
    }
    /**
     * Stocke les détections pour apprentissage
     */
    async storeDetections(anomalies, metrics) {
        try {
            for (const anomaly of anomalies) {
                await this.memory.storeGlobalPattern({
                    id: `detection-${anomaly.id}`,
                    content: {
                        type: 'anomaly-detection',
                        anomaly,
                        systemState: metrics,
                        detectorConfig: {
                            sensitivityLevel: this.sensitivityLevel,
                            thresholds: this.thresholds
                        }
                    },
                    domain: 'anomaly-detection',
                    crossDomainRelevance: 0.8,
                    timestamp: new Date(),
                    version: '1.0',
                    metadata: {
                        anomalyType: anomaly.type,
                        severity: anomaly.severity,
                        confidence: anomaly.confidence
                    }
                });
                // Ajout à l'historique local
                this.detectionHistory.push({
                    timestamp: new Date(),
                    anomaly,
                    metrics
                });
                // Limitation de l'historique
                if (this.detectionHistory.length > 1000) {
                    this.detectionHistory.shift();
                }
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du stockage des détections:', error);
        }
    }
    /**
     * Met à jour les statistiques de détection
     */
    updateDetectionStats(anomalies, processingTime) {
        this.detectionStats.totalDetections += anomalies.length;
        if (anomalies.length > 0) {
            const avgConfidence = anomalies.reduce((sum, a) => sum + a.confidence, 0) / anomalies.length;
            const totalConfidence = this.detectionStats.averageConfidence * (this.detectionStats.totalDetections - anomalies.length);
            this.detectionStats.averageConfidence = (totalConfidence + avgConfidence * anomalies.length) / this.detectionStats.totalDetections;
        }
        // Calcul du taux de détection (simplifié)
        this.detectionStats.detectionRate = this.detectionStats.totalDetections / Math.max(1, this.detectionHistory.length);
    }
    /**
     * Charge les modèles depuis la mémoire
     */
    async loadModelsFromMemory() {
        try {
            const models = await this.memory.queryAcrossDomains('anomaly-model', 10);
            for (const model of models) {
                if (model.content.type === 'baseline-metrics') {
                    // Chargement des métriques de base
                    Object.entries(model.content.metrics).forEach(([key, value]) => {
                        this.baselineMetrics.set(key, value);
                    });
                }
                else if (model.content.type === 'standard-deviations') {
                    // Chargement des écarts-types
                    Object.entries(model.content.deviations).forEach(([key, value]) => {
                        this.standardDeviations.set(key, value);
                    });
                }
            }
            logger_1.logger.info(`📊 ${models.length} modèles d'anomalie chargés`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du chargement des modèles:', error);
        }
    }
    /**
     * Charge les règles personnalisées
     */
    async loadCustomRules() {
        try {
            const rules = await this.memory.queryAcrossDomains('detection-rule', 20);
            for (const rule of rules) {
                if (rule.content.type === 'detection-rule') {
                    // Ajout de la règle personnalisée
                    this.detectionRules.set(rule.content.id, rule.content);
                }
            }
            logger_1.logger.info(`📋 ${rules.length} règles de détection personnalisées chargées`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du chargement des règles:', error);
        }
    }
    /**
     * Récupère les seuils par défaut
     */
    getDefaultThresholds() {
        return {
            systemLoad: { warning: 0.7, critical: 0.9 },
            memoryUsage: { warning: 0.8, critical: 0.95 },
            responseTime: { warning: 200, critical: 500 },
            errorRate: { warning: 0.05, critical: 0.1 },
            agentHealth: { warning: 0.7, critical: 0.5 },
            synapticHealth: { warning: 0.8, critical: 0.6 }
        };
    }
    /**
     * Récupère le statut du détecteur
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            sensitivityLevel: this.sensitivityLevel,
            rulesCount: this.detectionRules.size,
            baselineMetricsCount: this.baselineMetrics.size,
            detectionHistory: this.detectionHistory.length,
            stats: this.detectionStats,
            thresholds: this.thresholds
        };
    }
    /**
     * Arrêt gracieux
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Détecteur d\'Anomalies...');
        // Sauvegarde des modèles mis à jour
        await this.saveModelsToMemory();
        logger_1.logger.info('✅ Détecteur d\'Anomalies arrêté');
    }
    /**
     * Sauvegarde les modèles en mémoire
     */
    async saveModelsToMemory() {
        try {
            // Sauvegarde des métriques de base
            if (this.baselineMetrics.size > 0) {
                await this.memory.storeGlobalPattern({
                    id: `baseline-metrics-${Date.now()}`,
                    content: {
                        type: 'baseline-metrics',
                        metrics: Object.fromEntries(this.baselineMetrics)
                    },
                    domain: 'anomaly-detection',
                    crossDomainRelevance: 0.9,
                    timestamp: new Date(),
                    version: '1.0',
                    metadata: {
                        modelType: 'baseline-metrics',
                        metricsCount: this.baselineMetrics.size
                    }
                });
            }
            // Sauvegarde des écarts-types
            if (this.standardDeviations.size > 0) {
                await this.memory.storeGlobalPattern({
                    id: `standard-deviations-${Date.now()}`,
                    content: {
                        type: 'standard-deviations',
                        deviations: Object.fromEntries(this.standardDeviations)
                    },
                    domain: 'anomaly-detection',
                    crossDomainRelevance: 0.9,
                    timestamp: new Date(),
                    version: '1.0',
                    metadata: {
                        modelType: 'standard-deviations',
                        deviationsCount: this.standardDeviations.size
                    }
                });
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde des modèles:', error);
        }
    }
}
exports.AnomalyDetector = AnomalyDetector;
//# sourceMappingURL=AnomalyDetector.js.map