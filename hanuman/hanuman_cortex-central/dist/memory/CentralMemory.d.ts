import { EventEmitter } from 'events';
export interface MemoryConfig {
    weaviateUrl: string;
    redisUrl: string;
}
export interface Pattern {
    id: string;
    content: any;
    domain: string;
    crossDomainRelevance: number;
    timestamp: Date;
    version: string;
    metadata: any;
}
export interface CodeExperience {
    id: string;
    agentId: string;
    codeType: string;
    content: string;
    context: any;
    success: boolean;
    feedback: any;
    timestamp: Date;
}
export interface KnowledgeGraph {
    nodes: Array<{
        id: string;
        type: string;
        properties: any;
    }>;
    edges: Array<{
        from: string;
        to: string;
        relationship: string;
        weight: number;
    }>;
}
/**
 * Mémoire Centrale - Système de Stockage et Récupération des Connaissances
 *
 * Implémente un système de mémoire hiérarchisé avec:
 * - Mémoire vectorielle (Weaviate) pour les patterns et connaissances
 * - Mémoire de travail (Redis) pour les données temporaires
 * - Système d'oubli adaptatif pour la plasticité
 */
export declare class CentralMemory extends EventEmitter {
    private weaviate;
    private redis;
    private isInitialized;
    private readonly SCHEMAS;
    constructor(config: MemoryConfig);
    /**
     * Initialise le système de mémoire centrale
     */
    initialize(): Promise<void>;
    /**
     * Stocke un pattern global avec métadonnées
     */
    storeGlobalPattern(pattern: Pattern): Promise<void>;
    /**
     * Recherche cross-domaine de patterns
     */
    queryAcrossDomains(query: string, limit?: number): Promise<Pattern[]>;
    /**
     * Stocke une expérience de code avec importance calculée
     */
    storeCodeExperience(experience: CodeExperience): Promise<void>;
    /**
     * Stocke l'exécution d'une tâche
     */
    storeTaskExecution(taskData: any): Promise<void>;
    /**
     * Stocke une interaction d'agent
     */
    storeAgentInteraction(agentId: string, interaction: any): Promise<void>;
    /**
     * Récupère le statut d'une tâche
     */
    getTaskStatus(taskId: string): Promise<any>;
    /**
     * Stocke le résultat d'une tâche
     */
    storeTaskResult(taskId: string, result: any): Promise<void>;
    /**
     * Stocke le statut d'une tâche
     */
    storeTaskStatus(taskId: string, status: any): Promise<void>;
    /**
     * Calcule la pertinence cross-domaine d'un pattern
     */
    private calculateCrossDomainRelevance;
    /**
     * Calcule l'importance d'une expérience
     */
    private calculateImportance;
    /**
     * Calcule la rétention basée sur l'importance
     */
    private calculateRetention;
    /**
     * Trouve des expériences similaires
     */
    private findSimilarExperiences;
    /**
     * Met à jour les statistiques d'agent
     */
    private updateAgentStats;
    /**
     * Création des schémas Weaviate
     */
    private createWeaviateSchemas;
    /**
     * Crée un schéma s'il n'existe pas
     */
    private createSchemaIfNotExists;
    /**
     * Démarrage du système d'oubli adaptatif
     */
    private startAdaptiveForgetting;
    /**
     * Exécute l'oubli adaptatif
     */
    private performAdaptiveForgetting;
    /**
     * Démarrage du monitoring mémoire
     */
    private startMemoryMonitoring;
    /**
     * Vérification de santé mémoire
     */
    private performMemoryHealthCheck;
    /**
     * Récupère les statistiques d'utilisation
     */
    getUsageStats(): Promise<any>;
    /**
     * Parse les informations mémoire Redis
     */
    private parseRedisMemoryInfo;
    /**
     * Récupère le statut de la mémoire
     */
    getStatus(): any;
    /**
     * Récupère les statistiques générales
     */
    getStats(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=CentralMemory.d.ts.map