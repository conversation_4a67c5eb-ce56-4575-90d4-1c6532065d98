"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SynapticNetwork = exports.ConnectionType = exports.SignalPriority = exports.NeuralSignalType = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
var NeuralSignalType;
(function (NeuralSignalType) {
    // Signaux cognitifs
    NeuralSignalType["THOUGHT"] = "thought";
    NeuralSignalType["DECISION"] = "decision";
    NeuralSignalType["MEMORY_FORMATION"] = "memory_formation";
    NeuralSignalType["LEARNING"] = "learning";
    // Signaux sensoriels
    NeuralSignalType["SENSORY_INPUT"] = "sensory_input";
    NeuralSignalType["PERCEPTION"] = "perception";
    NeuralSignalType["ATTENTION"] = "attention";
    // Signaux moteurs
    NeuralSignalType["ACTION_INITIATION"] = "action_initiation";
    NeuralSignalType["MOTOR_COMMAND"] = "motor_command";
    NeuralSignalType["COORDINATION"] = "coordination";
    // Signaux émotionnels
    NeuralSignalType["EMOTION"] = "emotion";
    NeuralSignalType["MOTIVATION"] = "motivation";
    NeuralSignalType["REWARD"] = "reward";
    // Signaux système
    NeuralSignalType["HEALTH_CHECK"] = "health_check";
    NeuralSignalType["SYNCHRONIZATION"] = "synchronization";
    NeuralSignalType["ADAPTATION"] = "adaptation";
    NeuralSignalType["EVOLUTION"] = "evolution";
})(NeuralSignalType || (exports.NeuralSignalType = NeuralSignalType = {}));
var SignalPriority;
(function (SignalPriority) {
    SignalPriority[SignalPriority["CRITICAL"] = 0] = "CRITICAL";
    SignalPriority[SignalPriority["HIGH"] = 1] = "HIGH";
    SignalPriority[SignalPriority["NORMAL"] = 2] = "NORMAL";
    SignalPriority[SignalPriority["LOW"] = 3] = "LOW";
    SignalPriority[SignalPriority["BACKGROUND"] = 4] = "BACKGROUND"; // Signaux non urgents (apprentissage, optimisation)
})(SignalPriority || (exports.SignalPriority = SignalPriority = {}));
var ConnectionType;
(function (ConnectionType) {
    ConnectionType["EXCITATORY"] = "excitatory";
    ConnectionType["INHIBITORY"] = "inhibitory";
    ConnectionType["MODULATORY"] = "modulatory";
    ConnectionType["FEEDBACK"] = "feedback";
    ConnectionType["FEEDFORWARD"] = "feedforward"; // Connexion anticipatrice
})(ConnectionType || (exports.ConnectionType = ConnectionType = {}));
/**
 * Réseau Synaptique - Communication Neuronale Avancée
 *
 * Implémente un système de communication inspiré du cerveau humain
 * avec adaptation dynamique des connexions et routage intelligent.
 */
class SynapticNetwork extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        this.connections = new Map();
        this.signalQueue = new Map();
        this.routingTable = new Map();
        // Métriques réseau
        this.networkMetrics = {
            totalSignals: 0,
            successfulDeliveries: 0,
            failedDeliveries: 0,
            averageLatency: 0,
            networkEfficiency: 0,
            adaptationEvents: 0
        };
        this.memory = config.memory;
        this.adaptationEnabled = config.adaptationEnabled !== false;
        this.pruningEnabled = config.pruningEnabled !== false;
        this.learningRate = config.learningRate || 0.1;
        // Initialisation des files de priorité
        Object.values(SignalPriority).forEach(priority => {
            if (typeof priority === 'number') {
                this.signalQueue.set(priority, []);
            }
        });
        this.networkTopology = {
            nodes: [],
            connections: [],
            clusters: [],
            centralityScores: new Map(),
            pathLengths: new Map()
        };
    }
    /**
     * Initialise le réseau synaptique
     */
    async initialize() {
        try {
            logger_1.logger.info('🧠 Initialisation du Réseau Synaptique...');
            // Chargement de la topologie depuis la mémoire
            await this.loadNetworkTopology();
            // Initialisation des protocoles de communication
            await this.initializeCommunicationProtocols();
            // Démarrage du processeur de signaux
            this.startSignalProcessor();
            // Démarrage de l'adaptation réseau
            if (this.adaptationEnabled) {
                this.startNetworkAdaptation();
            }
            // Démarrage de l'élagage des connexions
            if (this.pruningEnabled) {
                this.startConnectionPruning();
            }
            this.isInitialized = true;
            logger_1.logger.info('✅ Réseau Synaptique initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Réseau Synaptique:', error);
            throw error;
        }
    }
    /**
     * Envoie un signal neural
     */
    async sendNeuralSignal(signal) {
        const completeSignal = {
            id: signal.id || this.generateSignalId(),
            type: signal.type,
            source: signal.source,
            target: signal.target,
            payload: signal.payload,
            priority: signal.priority || SignalPriority.NORMAL,
            timestamp: new Date(),
            ttl: signal.ttl || 30000, // 30 secondes par défaut
            route: signal.route
        };
        // Détermination de la route optimale
        if (!completeSignal.route && completeSignal.target) {
            completeSignal.route = await this.determineOptimalRoute(completeSignal.source, completeSignal.target, completeSignal.priority);
        }
        // Ajout à la file de priorité
        const queue = this.signalQueue.get(completeSignal.priority);
        queue.push(completeSignal);
        // Tri par priorité et timestamp
        queue.sort((a, b) => {
            if (a.priority !== b.priority) {
                return a.priority - b.priority;
            }
            return a.timestamp.getTime() - b.timestamp.getTime();
        });
        this.networkMetrics.totalSignals++;
        this.emit('signal-queued', {
            signal: completeSignal,
            queueSize: queue.length,
            timestamp: new Date()
        });
    }
    /**
     * Établit une connexion synaptique
     */
    async establishConnection(source, target, type = ConnectionType.EXCITATORY, initialStrength = 0.5) {
        const connectionId = `${source}->${target}`;
        const connection = {
            id: connectionId,
            source,
            target,
            strength: Math.max(0, Math.min(1, initialStrength)),
            type,
            lastUsed: new Date(),
            usageCount: 0,
            averageLatency: 0,
            successRate: 100,
            isActive: true
        };
        this.connections.set(connectionId, connection);
        // Mise à jour de la topologie
        await this.updateNetworkTopology();
        // Sauvegarde en mémoire
        await this.saveNetworkTopology();
        logger_1.logger.debug(`🔗 Connexion synaptique établie: ${connectionId} (force: ${initialStrength})`);
        this.emit('connection-established', {
            connection,
            timestamp: new Date()
        });
    }
    /**
     * Détermine la route optimale pour un signal
     */
    async determineOptimalRoute(source, target, priority) {
        // Vérification du cache de routage
        const cacheKey = `${source}->${target}-${priority}`;
        const cachedRoute = this.routingTable.get(cacheKey);
        if (cachedRoute && this.isRouteValid(cachedRoute)) {
            return cachedRoute;
        }
        // Calcul de la route optimale
        const route = await this.calculateOptimalRoute(source, target, priority);
        // Mise en cache
        this.routingTable.set(cacheKey, route);
        return route;
    }
    /**
     * Calcule la route optimale
     */
    async calculateOptimalRoute(source, target, priority) {
        // Algorithme de routage basé sur la priorité et les métriques réseau
        let protocol;
        let latency;
        let reliability;
        switch (priority) {
            case SignalPriority.CRITICAL:
                protocol = 'direct'; // Communication directe pour les signaux critiques
                latency = 1;
                reliability = 0.99;
                break;
            case SignalPriority.HIGH:
                protocol = 'nats'; // NATS pour les signaux temps réel
                latency = 5;
                reliability = 0.95;
                break;
            case SignalPriority.NORMAL:
                protocol = 'redis'; // Redis pour les signaux standard
                latency = 10;
                reliability = 0.90;
                break;
            case SignalPriority.LOW:
            case SignalPriority.BACKGROUND:
                protocol = 'kafka'; // Kafka pour les signaux non urgents
                latency = 50;
                reliability = 0.85;
                break;
            default:
                protocol = 'redis';
                latency = 10;
                reliability = 0.90;
        }
        // Calcul du chemin optimal dans la topologie
        const path = this.findShortestPath(source, target);
        return {
            protocol,
            path,
            latency,
            reliability
        };
    }
    /**
     * Trouve le chemin le plus court entre deux nœuds
     */
    findShortestPath(source, target) {
        // Implémentation simplifiée de Dijkstra
        const distances = new Map();
        const previous = new Map();
        const unvisited = new Set(this.networkTopology.nodes);
        // Initialisation
        distances.set(source, 0);
        this.networkTopology.nodes.forEach(node => {
            if (node !== source) {
                distances.set(node, Infinity);
            }
        });
        while (unvisited.size > 0) {
            // Nœud avec la distance minimale
            let current = Array.from(unvisited).reduce((min, node) => distances.get(node) < distances.get(min) ? node : min);
            if (current === target)
                break;
            unvisited.delete(current);
            // Mise à jour des distances des voisins
            const neighbors = this.getNeighbors(current);
            neighbors.forEach(neighbor => {
                if (unvisited.has(neighbor)) {
                    const connection = this.connections.get(`${current}->${neighbor}`);
                    const weight = connection ? (1 - connection.strength) : 1;
                    const alt = distances.get(current) + weight;
                    if (alt < distances.get(neighbor)) {
                        distances.set(neighbor, alt);
                        previous.set(neighbor, current);
                    }
                }
            });
        }
        // Reconstruction du chemin
        const path = [];
        let current = target;
        while (current !== undefined) {
            path.unshift(current);
            current = previous.get(current);
        }
        return path.length > 1 ? path : [source, target];
    }
    /**
     * Obtient les voisins d'un nœud
     */
    getNeighbors(node) {
        return Array.from(this.connections.values())
            .filter(conn => conn.source === node && conn.isActive)
            .map(conn => conn.target);
    }
    /**
     * Vérifie si une route est valide
     */
    isRouteValid(route) {
        // Vérification de la validité des nœuds dans le chemin
        return route.path.every(node => this.networkTopology.nodes.includes(node));
    }
    /**
     * Démarre le processeur de signaux
     */
    startSignalProcessor() {
        setInterval(async () => {
            await this.processSignalQueue();
        }, 10); // Traitement toutes les 10ms
        logger_1.logger.info('🔄 Processeur de signaux démarré');
    }
    /**
     * Traite la file de signaux
     */
    async processSignalQueue() {
        // Traitement par ordre de priorité
        for (const priority of [
            SignalPriority.CRITICAL,
            SignalPriority.HIGH,
            SignalPriority.NORMAL,
            SignalPriority.LOW,
            SignalPriority.BACKGROUND
        ]) {
            const queue = this.signalQueue.get(priority);
            if (queue.length > 0) {
                const signal = queue.shift();
                // Vérification du TTL
                if (Date.now() - signal.timestamp.getTime() > signal.ttl) {
                    this.networkMetrics.failedDeliveries++;
                    continue;
                }
                try {
                    await this.deliverSignal(signal);
                    this.networkMetrics.successfulDeliveries++;
                }
                catch (error) {
                    logger_1.logger.error('❌ Erreur lors de la livraison du signal:', error);
                    this.networkMetrics.failedDeliveries++;
                }
                // Traitement d'un seul signal par cycle pour les priorités élevées
                if (priority <= SignalPriority.HIGH) {
                    break;
                }
            }
        }
    }
    /**
     * Livre un signal à sa destination
     */
    async deliverSignal(signal) {
        const startTime = Date.now();
        try {
            if (signal.route) {
                switch (signal.route.protocol) {
                    case 'direct':
                        await this.deliverDirectSignal(signal);
                        break;
                    case 'kafka':
                        await this.deliverKafkaSignal(signal);
                        break;
                    case 'nats':
                        await this.deliverNATSSignal(signal);
                        break;
                    case 'redis':
                        await this.deliverRedisSignal(signal);
                        break;
                }
            }
            // Mise à jour des métriques de connexion
            if (signal.target) {
                await this.updateConnectionMetrics(signal.source, signal.target, true, Date.now() - startTime);
            }
            this.emit('signal-delivered', {
                signal,
                latency: Date.now() - startTime,
                timestamp: new Date()
            });
        }
        catch (error) {
            // Mise à jour des métriques d'échec
            if (signal.target) {
                await this.updateConnectionMetrics(signal.source, signal.target, false, Date.now() - startTime);
            }
            throw error;
        }
    }
    /**
     * Livre un signal en direct
     */
    async deliverDirectSignal(signal) {
        this.emit('neural-signal', signal);
    }
    /**
     * Livre un signal via Kafka
     */
    async deliverKafkaSignal(signal) {
        if (this.kafka) {
            const producer = this.kafka.producer();
            await producer.send({
                topic: `neural-signals-${signal.target}`,
                messages: [{
                        key: signal.id,
                        value: JSON.stringify(signal)
                    }]
            });
        }
        else {
            await this.deliverDirectSignal(signal);
        }
    }
    /**
     * Livre un signal via NATS
     */
    async deliverNATSSignal(signal) {
        if (this.nats) {
            this.nats.publish(`neural.${signal.target}`, JSON.stringify(signal));
        }
        else {
            await this.deliverDirectSignal(signal);
        }
    }
    /**
     * Livre un signal via Redis
     */
    async deliverRedisSignal(signal) {
        if (this.redis) {
            await this.redis.lpush(`neural:${signal.target}`, JSON.stringify(signal));
        }
        else {
            await this.deliverDirectSignal(signal);
        }
    }
    /**
     * Met à jour les métriques de connexion
     */
    async updateConnectionMetrics(source, target, success, latency) {
        const connectionId = `${source}->${target}`;
        const connection = this.connections.get(connectionId);
        if (connection) {
            connection.usageCount++;
            connection.lastUsed = new Date();
            connection.averageLatency = (connection.averageLatency + latency) / 2;
            if (success) {
                connection.successRate = Math.min(100, connection.successRate + 0.1);
            }
            else {
                connection.successRate = Math.max(0, connection.successRate - 1);
            }
            this.connections.set(connectionId, connection);
        }
    }
    /**
     * Démarre l'adaptation du réseau
     */
    startNetworkAdaptation() {
        setInterval(async () => {
            await this.adaptSynapticStrength();
        }, 60000); // Adaptation toutes les minutes
        logger_1.logger.info('🧠 Adaptation du réseau démarrée');
    }
    /**
     * Adapte la force synaptique
     */
    async adaptSynapticStrength() {
        for (const connection of this.connections.values()) {
            const usagePattern = this.analyzeUsagePattern(connection);
            if (usagePattern.frequency > 0.8) {
                // Renforcement des connexions fréquemment utilisées
                connection.strength = Math.min(1.0, connection.strength + this.learningRate);
                this.networkMetrics.adaptationEvents++;
            }
            else if (usagePattern.frequency < 0.2) {
                // Affaiblissement des connexions peu utilisées
                connection.strength = Math.max(0.0, connection.strength - this.learningRate);
            }
        }
        await this.saveNetworkTopology();
    }
    /**
     * Analyse le pattern d'usage d'une connexion
     */
    analyzeUsagePattern(connection) {
        const timeSinceLastUse = Date.now() - connection.lastUsed.getTime();
        const hoursSinceLastUse = timeSinceLastUse / (1000 * 60 * 60);
        // Calcul de fréquence basé sur l'usage récent
        const frequency = Math.max(0, 1 - (hoursSinceLastUse / 24)); // Décroissance sur 24h
        return { frequency };
    }
    /**
     * Démarre l'élagage des connexions
     */
    startConnectionPruning() {
        setInterval(async () => {
            await this.pruneWeakConnections();
        }, 300000); // Élagage toutes les 5 minutes
        logger_1.logger.info('✂️ Élagage des connexions démarré');
    }
    /**
     * Élague les connexions faibles
     */
    async pruneWeakConnections() {
        const connectionsToRemove = [];
        for (const [id, connection] of this.connections) {
            if (connection.strength < 0.1 && connection.successRate < 50) {
                connectionsToRemove.push(id);
            }
        }
        for (const id of connectionsToRemove) {
            this.connections.delete(id);
            logger_1.logger.debug(`✂️ Connexion élaguée: ${id}`);
        }
        if (connectionsToRemove.length > 0) {
            await this.updateNetworkTopology();
            await this.saveNetworkTopology();
        }
    }
    /**
     * Initialise les protocoles de communication
     */
    async initializeCommunicationProtocols() {
        // Initialisation simplifiée - dans une vraie implémentation,
        // on initialiserait Kafka, NATS et Redis
        logger_1.logger.info('📡 Protocoles de communication initialisés');
    }
    /**
     * Met à jour la topologie du réseau
     */
    async updateNetworkTopology() {
        // Extraction des nœuds uniques
        const nodes = new Set();
        const connections = Array.from(this.connections.values());
        connections.forEach(conn => {
            nodes.add(conn.source);
            nodes.add(conn.target);
        });
        this.networkTopology = {
            nodes: Array.from(nodes),
            connections,
            clusters: this.detectClusters(Array.from(nodes), connections),
            centralityScores: this.calculateCentralityScores(Array.from(nodes), connections),
            pathLengths: new Map()
        };
    }
    /**
     * Détecte les clusters dans le réseau
     */
    detectClusters(nodes, connections) {
        // Implémentation simplifiée de détection de clusters
        return [];
    }
    /**
     * Calcule les scores de centralité
     */
    calculateCentralityScores(nodes, connections) {
        const scores = new Map();
        nodes.forEach(node => {
            const inDegree = connections.filter(c => c.target === node).length;
            const outDegree = connections.filter(c => c.source === node).length;
            scores.set(node, inDegree + outDegree);
        });
        return scores;
    }
    /**
     * Charge la topologie depuis la mémoire
     */
    async loadNetworkTopology() {
        try {
            const topology = await this.memory.retrieve('network_topology');
            if (topology) {
                this.networkTopology = topology;
                // Reconstruction des connexions
                topology.connections.forEach((conn) => {
                    this.connections.set(conn.id, conn);
                });
                logger_1.logger.info(`🧠 Topologie chargée: ${topology.nodes.length} nœuds, ${topology.connections.length} connexions`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger la topologie réseau:', error);
        }
    }
    /**
     * Sauvegarde la topologie en mémoire
     */
    async saveNetworkTopology() {
        try {
            await this.memory.store('network_topology', this.networkTopology);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde de la topologie:', error);
        }
    }
    /**
     * Génère un ID unique pour un signal
     */
    generateSignalId() {
        return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Obtient les métriques du réseau
     */
    getNetworkMetrics() {
        const efficiency = this.networkMetrics.totalSignals > 0 ?
            this.networkMetrics.successfulDeliveries / this.networkMetrics.totalSignals : 0;
        return {
            ...this.networkMetrics,
            networkEfficiency: efficiency,
            activeConnections: this.connections.size,
            averageConnectionStrength: this.calculateAverageConnectionStrength(),
            topologyStats: {
                nodes: this.networkTopology.nodes.length,
                connections: this.networkTopology.connections.length,
                clusters: this.networkTopology.clusters.length
            }
        };
    }
    /**
     * Calcule la force moyenne des connexions
     */
    calculateAverageConnectionStrength() {
        if (this.connections.size === 0)
            return 0;
        const totalStrength = Array.from(this.connections.values())
            .reduce((sum, conn) => sum + conn.strength, 0);
        return totalStrength / this.connections.size;
    }
    /**
     * Arrêt gracieux du réseau
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Réseau Synaptique...');
        // Sauvegarde finale
        await this.saveNetworkTopology();
        // Fermeture des connexions
        if (this.kafka)
            await this.kafka.disconnect();
        if (this.nats)
            await this.nats.close();
        if (this.redis)
            await this.redis.disconnect();
        this.isInitialized = false;
        logger_1.logger.info('✅ Réseau Synaptique arrêté');
    }
}
exports.SynapticNetwork = SynapticNetwork;
//# sourceMappingURL=SynapticNetwork.js.map