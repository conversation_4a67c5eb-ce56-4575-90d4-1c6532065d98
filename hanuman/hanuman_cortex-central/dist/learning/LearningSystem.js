"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LearningSystem = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
/**
 * Système d'Apprentissage Continu
 *
 * Implémente l'apprentissage automatique et l'adaptation continue
 * du système basé sur les expériences passées et les résultats obtenus.
 */
class LearningSystem extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        // Patterns d'apprentissage
        this.learningPatterns = new Map();
        // Règles d'adaptation
        this.adaptationRules = new Map();
        // Métriques d'apprentissage
        this.metrics = {
            totalPatterns: 0,
            successfulAdaptations: 0,
            failedAdaptations: 0,
            averageConfidence: 0,
            learningRate: 0.1,
            adaptationAccuracy: 0,
            lastLearningEvent: new Date()
        };
        // Cache des prédictions
        this.predictionCache = new Map();
        this.config = config;
        this.memory = config.memory;
        this.decisionEngine = config.decisionEngine;
        this.neuralNetwork = config.neuralNetwork;
        this.metrics.learningRate = config.learningRate || 0.1;
    }
    /**
     * Initialise le système d'apprentissage
     */
    async initialize() {
        try {
            logger_1.logger.info('🧠 Initialisation du Système d\'Apprentissage...');
            // Chargement des patterns existants
            await this.loadExistingPatterns();
            // Chargement des règles d'adaptation
            await this.loadAdaptationRules();
            // Configuration des événements d'apprentissage
            this.setupLearningEvents();
            // Démarrage de l'apprentissage continu
            this.startContinuousLearning();
            // Démarrage de l'optimisation des patterns
            this.startPatternOptimization();
            this.isInitialized = true;
            logger_1.logger.info('✅ Système d\'Apprentissage initialisé avec succès');
            this.emit('learning-system-initialized', {
                timestamp: new Date(),
                patternsLoaded: this.learningPatterns.size,
                rulesLoaded: this.adaptationRules.size
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Système d\'Apprentissage:', error);
            throw error;
        }
    }
    /**
     * Apprend d'une expérience
     */
    async learnFromExperience(experience) {
        try {
            const pattern = {
                id: `pattern-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                type: experience.success ? 'success' : 'failure',
                context: experience.context,
                action: experience.action,
                result: experience.result,
                confidence: this.calculateConfidence(experience),
                timestamp: new Date(),
                domain: experience.domain,
                metadata: experience.metadata || {}
            };
            // Stockage du pattern
            await this.storePattern(pattern);
            // Mise à jour des règles d'adaptation
            await this.updateAdaptationRules(pattern);
            // Mise à jour des métriques
            this.updateLearningMetrics(pattern);
            // Émission d'événement d'apprentissage
            this.emit('pattern-learned', {
                pattern,
                timestamp: new Date()
            });
            logger_1.logger.info(`📚 Nouveau pattern appris: ${pattern.id} (${pattern.type})`);
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'apprentissage:', error);
            throw error;
        }
    }
    /**
     * Prédit le meilleur plan d'action
     */
    async predictBestAction(context, domain) {
        try {
            // Recherche de patterns similaires
            const similarPatterns = await this.findSimilarPatterns(context, domain);
            if (similarPatterns.length === 0) {
                return {
                    action: null,
                    confidence: 0,
                    reasoning: 'Aucun pattern similaire trouvé'
                };
            }
            // Analyse des patterns de succès
            const successPatterns = similarPatterns.filter(p => p.type === 'success');
            if (successPatterns.length === 0) {
                return {
                    action: null,
                    confidence: 0,
                    reasoning: 'Aucun pattern de succès trouvé'
                };
            }
            // Sélection du meilleur pattern
            const bestPattern = this.selectBestPattern(successPatterns, context);
            return {
                action: bestPattern.action,
                confidence: bestPattern.confidence,
                reasoning: `Basé sur ${successPatterns.length} patterns de succès similaires`
            };
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de la prédiction:', error);
            throw error;
        }
    }
    /**
     * Adapte le comportement du système
     */
    async adaptBehavior(trigger, context) {
        try {
            // Recherche de règles d'adaptation applicables
            const applicableRules = Array.from(this.adaptationRules.values())
                .filter(rule => this.evaluateCondition(rule.condition, context))
                .sort((a, b) => b.priority - a.priority);
            if (applicableRules.length === 0) {
                return false;
            }
            // Application de la meilleure règle
            const bestRule = applicableRules[0];
            const success = await this.applyAdaptationRule(bestRule, context);
            // Mise à jour des statistiques de la règle
            bestRule.usageCount++;
            bestRule.lastUsed = new Date();
            if (success) {
                bestRule.successRate = (bestRule.successRate * (bestRule.usageCount - 1) + 1) / bestRule.usageCount;
                this.metrics.successfulAdaptations++;
            }
            else {
                bestRule.successRate = (bestRule.successRate * (bestRule.usageCount - 1)) / bestRule.usageCount;
                this.metrics.failedAdaptations++;
            }
            // Mise à jour de la précision d'adaptation
            this.metrics.adaptationAccuracy =
                this.metrics.successfulAdaptations /
                    (this.metrics.successfulAdaptations + this.metrics.failedAdaptations);
            this.emit('behavior-adapted', {
                rule: bestRule,
                success,
                context,
                timestamp: new Date()
            });
            return success;
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'adaptation:', error);
            return false;
        }
    }
    /**
     * Optimise les patterns d'apprentissage
     */
    async optimizePatterns() {
        try {
            logger_1.logger.info('🔧 Optimisation des patterns d\'apprentissage...');
            // Suppression des patterns obsolètes
            await this.removeObsoletePatterns();
            // Fusion des patterns similaires
            await this.mergeSimilarPatterns();
            // Mise à jour des confidences
            await this.updatePatternConfidences();
            // Nettoyage du cache
            this.predictionCache.clear();
            logger_1.logger.info('✅ Optimisation des patterns terminée');
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'optimisation:', error);
        }
    }
    /**
     * Calcule la confiance d'un pattern
     */
    calculateConfidence(experience) {
        let confidence = 0.5; // Base
        // Facteurs d'augmentation de confiance
        if (experience.success)
            confidence += 0.3;
        if (experience.metadata?.executionTime < 1000)
            confidence += 0.1;
        if (experience.metadata?.resourceUsage < 0.5)
            confidence += 0.1;
        // Normalisation
        return Math.min(Math.max(confidence, 0), 1);
    }
    /**
     * Stocke un pattern d'apprentissage
     */
    async storePattern(pattern) {
        // Stockage en mémoire locale
        this.learningPatterns.set(pattern.id, pattern);
        // Stockage en mémoire centrale
        await this.memory.storeGlobalPattern({
            id: pattern.id,
            content: {
                type: pattern.type,
                context: pattern.context,
                action: pattern.action,
                result: pattern.result
            },
            domain: pattern.domain,
            timestamp: pattern.timestamp,
            version: '1.0',
            metadata: {
                confidence: pattern.confidence,
                learningSystem: true,
                ...pattern.metadata
            }
        });
        this.metrics.totalPatterns++;
    }
    /**
     * Charge les patterns existants
     */
    async loadExistingPatterns() {
        try {
            // Chargement depuis la mémoire centrale
            const patterns = await this.memory.searchPatterns({
                domain: '*',
                filters: { learningSystem: true },
                limit: this.config.maxPatterns || 10000
            });
            for (const pattern of patterns) {
                const learningPattern = {
                    id: pattern.id,
                    type: pattern.content.type,
                    context: pattern.content.context,
                    action: pattern.content.action,
                    result: pattern.content.result,
                    confidence: pattern.metadata.confidence || 0.5,
                    timestamp: new Date(pattern.timestamp),
                    domain: pattern.domain,
                    metadata: pattern.metadata
                };
                this.learningPatterns.set(pattern.id, learningPattern);
            }
            this.metrics.totalPatterns = this.learningPatterns.size;
            logger_1.logger.info(`📚 ${this.learningPatterns.size} patterns chargés`);
        }
        catch (error) {
            logger_1.logger.error('Erreur lors du chargement des patterns:', error);
        }
    }
    /**
     * Trouve des patterns similaires
     */
    async findSimilarPatterns(context, domain) {
        const similarPatterns = [];
        for (const pattern of this.learningPatterns.values()) {
            if (pattern.domain === domain || domain === '*') {
                const similarity = this.calculateSimilarity(context, pattern.context);
                if (similarity > 0.7) { // Seuil de similarité
                    similarPatterns.push(pattern);
                }
            }
        }
        return similarPatterns.sort((a, b) => b.confidence - a.confidence);
    }
    /**
     * Calcule la similarité entre deux contextes
     */
    calculateSimilarity(context1, context2) {
        // Implémentation basique - à améliorer selon les besoins
        const keys1 = Object.keys(context1);
        const keys2 = Object.keys(context2);
        const commonKeys = keys1.filter(key => keys2.includes(key));
        const totalKeys = new Set([...keys1, ...keys2]).size;
        if (totalKeys === 0)
            return 0;
        let matchingValues = 0;
        for (const key of commonKeys) {
            if (context1[key] === context2[key]) {
                matchingValues++;
            }
        }
        return (commonKeys.length + matchingValues) / (totalKeys + commonKeys.length);
    }
    /**
     * Sélectionne le meilleur pattern
     */
    selectBestPattern(patterns, context) {
        return patterns.reduce((best, current) => {
            const currentScore = current.confidence * this.calculateSimilarity(context, current.context);
            const bestScore = best.confidence * this.calculateSimilarity(context, best.context);
            return currentScore > bestScore ? current : best;
        });
    }
    /**
     * Configure les événements d'apprentissage
     */
    setupLearningEvents() {
        // Écoute des événements du système pour apprentissage automatique
        this.decisionEngine.on('decision-made', (data) => {
            this.learnFromDecision(data);
        });
        this.neuralNetwork.on('task-completed', (data) => {
            this.learnFromTaskCompletion(data);
        });
    }
    /**
     * Apprend d'une décision
     */
    async learnFromDecision(data) {
        // Implémentation de l'apprentissage basé sur les décisions
    }
    /**
     * Apprend de l'achèvement d'une tâche
     */
    async learnFromTaskCompletion(data) {
        // Implémentation de l'apprentissage basé sur les tâches
    }
    /**
     * Démarre l'apprentissage continu
     */
    startContinuousLearning() {
        setInterval(async () => {
            await this.performContinuousLearning();
        }, 300000); // Toutes les 5 minutes
    }
    /**
     * Effectue l'apprentissage continu
     */
    async performContinuousLearning() {
        try {
            // Analyse des patterns récents
            await this.analyzeRecentPatterns();
            // Mise à jour des règles d'adaptation
            await this.updateAdaptationRulesFromPatterns();
            // Optimisation automatique
            if (this.metrics.totalPatterns > 1000) {
                await this.optimizePatterns();
            }
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'apprentissage continu:', error);
        }
    }
    /**
     * Démarre l'optimisation des patterns
     */
    startPatternOptimization() {
        setInterval(async () => {
            await this.optimizePatterns();
        }, 3600000); // Toutes les heures
    }
    /**
     * Met à jour les métriques d'apprentissage
     */
    updateLearningMetrics(pattern) {
        this.metrics.lastLearningEvent = new Date();
        // Calcul de la confiance moyenne
        const totalConfidence = Array.from(this.learningPatterns.values())
            .reduce((sum, p) => sum + p.confidence, 0);
        this.metrics.averageConfidence = totalConfidence / this.learningPatterns.size;
    }
    /**
     * Charge les règles d'adaptation
     */
    async loadAdaptationRules() {
        // Règles d'adaptation par défaut
        const defaultRules = [
            {
                id: 'performance-optimization',
                condition: 'responseTime > 5000',
                action: 'optimize-performance',
                priority: 90,
                successRate: 0.8,
                usageCount: 0,
                lastUsed: new Date()
            },
            {
                id: 'error-recovery',
                condition: 'errorRate > 0.1',
                action: 'activate-recovery',
                priority: 95,
                successRate: 0.9,
                usageCount: 0,
                lastUsed: new Date()
            }
        ];
        for (const rule of defaultRules) {
            this.adaptationRules.set(rule.id, rule);
        }
    }
    /**
     * Met à jour les règles d'adaptation
     */
    async updateAdaptationRules(pattern) {
        // Logique de mise à jour des règles basée sur les nouveaux patterns
        if (pattern.type === 'success' && pattern.confidence > 0.8) {
            // Créer ou renforcer une règle d'adaptation
            const ruleId = `auto-rule-${pattern.domain}-${Date.now()}`;
            if (!this.adaptationRules.has(ruleId)) {
                const newRule = {
                    id: ruleId,
                    condition: this.generateConditionFromPattern(pattern),
                    action: this.generateActionFromPattern(pattern),
                    priority: Math.floor(pattern.confidence * 100),
                    successRate: pattern.confidence,
                    usageCount: 1,
                    lastUsed: new Date()
                };
                this.adaptationRules.set(ruleId, newRule);
            }
        }
    }
    /**
     * Génère une condition à partir d'un pattern
     */
    generateConditionFromPattern(pattern) {
        // Logique simplifiée pour générer des conditions
        return `domain === '${pattern.domain}' && complexity === '${pattern.context.complexity || 'medium'}'`;
    }
    /**
     * Génère une action à partir d'un pattern
     */
    generateActionFromPattern(pattern) {
        // Logique simplifiée pour générer des actions
        return `apply-pattern-${pattern.id}`;
    }
    /**
     * Évalue une condition
     */
    evaluateCondition(condition, context) {
        try {
            // Évaluation sécurisée des conditions
            // Dans un environnement de production, utiliser un parser plus sécurisé
            const func = new Function('context', `with(context) { return ${condition}; }`);
            return func(context);
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'évaluation de la condition:', error);
            return false;
        }
    }
    /**
     * Applique une règle d'adaptation
     */
    async applyAdaptationRule(rule, context) {
        try {
            logger_1.logger.info(`🔧 Application de la règle d'adaptation: ${rule.id}`);
            // Logique d'application des règles
            switch (rule.action) {
                case 'optimize-performance':
                    return await this.optimizePerformance(context);
                case 'activate-recovery':
                    return await this.activateRecovery(context);
                default:
                    logger_1.logger.warn(`Action inconnue: ${rule.action}`);
                    return false;
            }
        }
        catch (error) {
            logger_1.logger.error('Erreur lors de l\'application de la règle:', error);
            return false;
        }
    }
    /**
     * Optimise les performances
     */
    async optimizePerformance(context) {
        // Implémentation de l'optimisation des performances
        logger_1.logger.info('🚀 Optimisation des performances en cours...');
        return true;
    }
    /**
     * Active la récupération
     */
    async activateRecovery(context) {
        // Implémentation de la récupération
        logger_1.logger.info('🔄 Activation de la récupération...');
        return true;
    }
    /**
     * Supprime les patterns obsolètes
     */
    async removeObsoletePatterns() {
        const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 jours
        for (const [id, pattern] of this.learningPatterns.entries()) {
            if (pattern.timestamp < cutoffDate && pattern.confidence < 0.3) {
                this.learningPatterns.delete(id);
                this.metrics.totalPatterns--;
            }
        }
    }
    /**
     * Fusionne les patterns similaires
     */
    async mergeSimilarPatterns() {
        // Logique de fusion des patterns similaires
        const patterns = Array.from(this.learningPatterns.values());
        for (let i = 0; i < patterns.length; i++) {
            for (let j = i + 1; j < patterns.length; j++) {
                const similarity = this.calculateSimilarity(patterns[i].context, patterns[j].context);
                if (similarity > 0.95 && patterns[i].domain === patterns[j].domain) {
                    // Fusionner les patterns
                    const mergedPattern = this.mergePatterns(patterns[i], patterns[j]);
                    this.learningPatterns.set(mergedPattern.id, mergedPattern);
                    this.learningPatterns.delete(patterns[j].id);
                    patterns.splice(j, 1);
                    j--;
                }
            }
        }
    }
    /**
     * Fusionne deux patterns
     */
    mergePatterns(pattern1, pattern2) {
        return {
            id: pattern1.id,
            type: pattern1.type,
            context: { ...pattern1.context, ...pattern2.context },
            action: pattern1.action,
            result: pattern1.result,
            confidence: (pattern1.confidence + pattern2.confidence) / 2,
            timestamp: new Date(),
            domain: pattern1.domain,
            metadata: { ...pattern1.metadata, merged: true, originalIds: [pattern1.id, pattern2.id] }
        };
    }
    /**
     * Met à jour les confidences des patterns
     */
    async updatePatternConfidences() {
        for (const pattern of this.learningPatterns.values()) {
            // Dégrade la confiance des anciens patterns
            const ageInDays = (Date.now() - pattern.timestamp.getTime()) / (24 * 60 * 60 * 1000);
            const ageFactor = Math.max(0.1, 1 - (ageInDays / 365)); // Dégrade sur 1 an
            pattern.confidence *= ageFactor;
        }
    }
    /**
     * Analyse les patterns récents
     */
    async analyzeRecentPatterns() {
        const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 heures
        const recentPatterns = Array.from(this.learningPatterns.values())
            .filter(p => p.timestamp > recentDate);
        if (recentPatterns.length > 0) {
            const successRate = recentPatterns.filter(p => p.type === 'success').length / recentPatterns.length;
            this.emit('learning-analysis', {
                recentPatterns: recentPatterns.length,
                successRate,
                timestamp: new Date()
            });
        }
    }
    /**
     * Met à jour les règles d'adaptation à partir des patterns
     */
    async updateAdaptationRulesFromPatterns() {
        // Analyse des patterns pour créer de nouvelles règles
        const successPatterns = Array.from(this.learningPatterns.values())
            .filter(p => p.type === 'success' && p.confidence > 0.8);
        // Grouper par domaine
        const domainGroups = new Map();
        for (const pattern of successPatterns) {
            if (!domainGroups.has(pattern.domain)) {
                domainGroups.set(pattern.domain, []);
            }
            domainGroups.get(pattern.domain).push(pattern);
        }
        // Créer des règles pour chaque domaine avec suffisamment de patterns
        for (const [domain, patterns] of domainGroups.entries()) {
            if (patterns.length >= 5) { // Seuil minimum
                const avgConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length;
                const ruleId = `domain-rule-${domain}`;
                if (!this.adaptationRules.has(ruleId)) {
                    const newRule = {
                        id: ruleId,
                        condition: `domain === '${domain}'`,
                        action: `optimize-for-${domain}`,
                        priority: Math.floor(avgConfidence * 100),
                        successRate: avgConfidence,
                        usageCount: 0,
                        lastUsed: new Date()
                    };
                    this.adaptationRules.set(ruleId, newRule);
                }
            }
        }
    }
    /**
     * Obtient les métriques d'apprentissage
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Vérifie si le système est initialisé
     */
    isReady() {
        return this.isInitialized;
    }
}
exports.LearningSystem = LearningSystem;
//# sourceMappingURL=LearningSystem.js.map