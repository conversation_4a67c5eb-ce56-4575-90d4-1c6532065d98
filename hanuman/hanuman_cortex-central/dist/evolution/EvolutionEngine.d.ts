/**
 * EvolutionEngine - Moteur d'évolution continue pour l'adaptation du système
 * Implémente la neuroplasticité artificielle pour l'amélioration continue
 */
import { EventEmitter } from 'events';
import { EvolutionPlan, EvolutionReport, EvolutionConfig, EvolutionMetrics } from './types';
export declare class EvolutionEngine extends EventEmitter {
    private logger;
    private config;
    private technologyScanner;
    private impactAnalyzer;
    private evolutionPlanner;
    private agentTrainer;
    private deploymentManager;
    private validationSystem;
    private isRunning;
    private currentEvolutions;
    private metrics;
    private scanTimer?;
    constructor(config: EvolutionConfig);
    /**
     * Démarre le moteur d'évolution
     */
    start(): Promise<void>;
    /**
     * Arrête le moteur d'évolution
     */
    stop(): Promise<void>;
    /**
     * Exécute un cycle d'évolution complet
     */
    performEvolutionCycle(): Promise<EvolutionReport>;
    /**
     * Scan du paysage technologique
     */
    private scanTechnologyLandscape;
    /**
     * Analyse d'impact des technologies
     */
    private analyzeImpact;
    /**
     * Création du plan d'évolution
     */
    private createEvolutionPlan;
    /**
     * Formation des agents
     */
    private trainAgents;
    /**
     * Déploiement des agents mis à jour
     */
    private deployUpdatedAgents;
    /**
     * Validation du déploiement
     */
    private validateDeployment;
    /**
     * Démarrage du scan périodique
     */
    private startPeriodicScanning;
    /**
     * Attendre la fin des évolutions en cours
     */
    private waitForCurrentEvolutions;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Émission d'un événement d'évolution
     */
    private emitEvent;
    /**
     * Initialisation des métriques
     */
    private initializeMetrics;
    /**
     * Mise à jour des métriques
     */
    private updateMetrics;
    /**
     * Création d'un rapport vide
     */
    private createEmptyReport;
    /**
     * Génération du rapport d'évolution
     */
    private generateEvolutionReport;
    /**
     * Génération des recommandations
     */
    private generateRecommendations;
    /**
     * Génération des leçons apprises
     */
    private generateLessons;
    /**
     * Getters pour l'état et les métriques
     */
    isEvolutionRunning(): boolean;
    getMetrics(): EvolutionMetrics;
    getCurrentEvolutions(): EvolutionPlan[];
    getConfig(): EvolutionConfig;
}
//# sourceMappingURL=EvolutionEngine.d.ts.map