/**
 * EvolutionPlanner - Planificateur d'évolution pour l'orchestration des mises à jour
 * Crée des plans d'évolution optimisés basés sur l'analyse d'impact
 */
import { EventEmitter } from 'events';
import { ImpactAnalysis, EvolutionPlan } from './types';
export declare class EvolutionPlanner extends EventEmitter {
    private logger;
    private planHistory;
    private planTemplates;
    constructor();
    /**
     * Initialise le planificateur d'évolution
     */
    initialize(): Promise<void>;
    /**
     * Crée un plan d'évolution basé sur les analyses d'impact
     */
    createPlan(impactAnalyses: ImpactAnalysis[]): Promise<EvolutionPlan>;
    /**
     * Sélectionne les technologies à inclure dans le plan
     */
    private selectTechnologiesForPlan;
    /**
     * Crée les phases d'évolution
     */
    private createEvolutionPhases;
    /**
     * Groupe les technologies par phase d'évolution
     */
    private groupTechnologiesByPhase;
    /**
     * Évalue les risques globaux du plan
     */
    private assessOverallRisks;
    /**
     * C<PERSON>e le plan de rollback
     */
    private createRollbackPlan;
    /**
     * C<PERSON>e les critères de validation pour une phase
     */
    private createValidationCriteria;
    /**
     * Méthodes utilitaires
     */
    private hasConflicts;
    private technologiesConflict;
    private getUniqueAgents;
    private getPhaseDependencies;
    private calculatePhaseDuration;
    private generatePlanId;
    private generatePlanName;
    private generatePlanDescription;
    private generatePhaseName;
    private generatePhaseDescription;
    private defineRollbackTriggers;
    private definePrerequisites;
    private defineDeliverables;
    private determinePlanPriority;
    private identifyStakeholders;
    private defineSuccessCriteria;
    private calculateOverallRisk;
    private generateMitigationStrategies;
    private generateContingencyPlans;
    private validatePlan;
    private loadPlanTemplates;
    private loadPlanHistory;
    /**
     * Getters
     */
    getPlanHistory(): EvolutionPlan[];
    getPlanById(id: string): EvolutionPlan | undefined;
}
//# sourceMappingURL=EvolutionPlanner.d.ts.map