"use strict";
/**
 * EvolutionPlanner - Planificateur d'évolution pour l'orchestration des mises à jour
 * Crée des plans d'évolution optimisés basés sur l'analyse d'impact
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvolutionPlanner = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
class EvolutionPlanner extends events_1.EventEmitter {
    constructor() {
        super();
        this.planHistory = [];
        this.planTemplates = new Map();
        this.logger = new logger_1.Logger('EvolutionPlanner');
    }
    /**
     * Initialise le planificateur d'évolution
     */
    async initialize() {
        this.logger.info('Initializing EvolutionPlanner...');
        try {
            // Chargement des templates de plans
            await this.loadPlanTemplates();
            // Chargement de l'historique des plans
            await this.loadPlanHistory();
            this.logger.info('EvolutionPlanner initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize EvolutionPlanner:', error);
            throw error;
        }
    }
    /**
     * Crée un plan d'évolution basé sur les analyses d'impact
     */
    async createPlan(impactAnalyses) {
        this.logger.info(`Creating evolution plan for ${impactAnalyses.length} technologies`);
        try {
            // Tri des analyses par score d'impact
            const sortedAnalyses = impactAnalyses.sort((a, b) => b.impactScore - a.impactScore);
            // Sélection des technologies à inclure
            const selectedAnalyses = this.selectTechnologiesForPlan(sortedAnalyses);
            // Création des phases d'évolution
            const phases = await this.createEvolutionPhases(selectedAnalyses);
            // Évaluation des risques globaux
            const riskAssessment = await this.assessOverallRisks(selectedAnalyses);
            // Création du plan de rollback
            const rollbackPlan = await this.createRollbackPlan(phases);
            // Calcul de la durée totale
            const totalDuration = phases.reduce((sum, phase) => sum + phase.estimatedDuration, 0);
            // Détermination de la priorité
            const priority = this.determinePlanPriority(selectedAnalyses, riskAssessment);
            const plan = {
                id: this.generatePlanId(),
                name: this.generatePlanName(selectedAnalyses),
                description: this.generatePlanDescription(selectedAnalyses),
                technologies: selectedAnalyses.map(a => a.technology),
                phases,
                totalDuration,
                priority,
                status: 'planned',
                createdAt: new Date(),
                rollbackPlan,
                stakeholders: this.identifyStakeholders(selectedAnalyses),
                successCriteria: this.defineSuccessCriteria(selectedAnalyses),
                riskAssessment
            };
            // Validation du plan
            await this.validatePlan(plan);
            // Sauvegarde du plan
            this.planHistory.push(plan);
            this.logger.info(`Evolution plan created: ${plan.name} (${plan.phases.length} phases, ${plan.totalDuration}h)`);
            return plan;
        }
        catch (error) {
            this.logger.error('Failed to create evolution plan:', error);
            throw error;
        }
    }
    /**
     * Sélectionne les technologies à inclure dans le plan
     */
    selectTechnologiesForPlan(analyses) {
        const selected = [];
        const maxTechnologies = 5; // Limite pour éviter des plans trop complexes
        const minImpactScore = 60; // Score minimum pour inclusion
        for (const analysis of analyses) {
            if (selected.length >= maxTechnologies) {
                break;
            }
            if (analysis.impactScore >= minImpactScore) {
                // Vérification des conflits avec les technologies déjà sélectionnées
                if (!this.hasConflicts(analysis, selected)) {
                    selected.push(analysis);
                }
            }
        }
        return selected;
    }
    /**
     * Crée les phases d'évolution
     */
    async createEvolutionPhases(analyses) {
        const phases = [];
        // Groupement des technologies par complexité et dépendances
        const phaseGroups = this.groupTechnologiesByPhase(analyses);
        for (let i = 0; i < phaseGroups.length; i++) {
            const group = phaseGroups[i];
            const phaseNumber = i + 1;
            const phase = {
                id: `phase_${phaseNumber}_${Date.now()}`,
                name: `Phase ${phaseNumber}: ${this.generatePhaseName(group)}`,
                description: this.generatePhaseDescription(group),
                agentsToUpdate: this.getUniqueAgents(group),
                dependencies: this.getPhaseDependencies(group, phases),
                estimatedDuration: this.calculatePhaseDuration(group),
                status: 'pending',
                validationCriteria: this.createValidationCriteria(group),
                rollbackTriggers: this.defineRollbackTriggers(group),
                prerequisites: this.definePrerequisites(group),
                deliverables: this.defineDeliverables(group)
            };
            phases.push(phase);
        }
        return phases;
    }
    /**
     * Groupe les technologies par phase d'évolution
     */
    groupTechnologiesByPhase(analyses) {
        const groups = [];
        const processed = new Set();
        // Phase 1: Technologies à faible complexité et haute valeur
        const lowComplexityHighValue = analyses.filter(a => a.migrationComplexity === 'low' &&
            a.businessValue > 70 &&
            !processed.has(a.technology.id));
        if (lowComplexityHighValue.length > 0) {
            groups.push(lowComplexityHighValue);
            lowComplexityHighValue.forEach(a => processed.add(a.technology.id));
        }
        // Phase 2: Technologies à complexité moyenne
        const mediumComplexity = analyses.filter(a => a.migrationComplexity === 'medium' &&
            !processed.has(a.technology.id));
        if (mediumComplexity.length > 0) {
            groups.push(mediumComplexity);
            mediumComplexity.forEach(a => processed.add(a.technology.id));
        }
        // Phase 3: Technologies à haute complexité
        const highComplexity = analyses.filter(a => (a.migrationComplexity === 'high' || a.migrationComplexity === 'very_high') &&
            !processed.has(a.technology.id));
        if (highComplexity.length > 0) {
            groups.push(highComplexity);
            highComplexity.forEach(a => processed.add(a.technology.id));
        }
        return groups.filter(group => group.length > 0);
    }
    /**
     * Évalue les risques globaux du plan
     */
    async assessOverallRisks(analyses) {
        const risks = [];
        // Risques techniques
        const highComplexityCount = analyses.filter(a => a.migrationComplexity === 'high' || a.migrationComplexity === 'very_high').length;
        if (highComplexityCount > 0) {
            risks.push({
                id: 'tech_complexity',
                description: `${highComplexityCount} technologies with high migration complexity`,
                probability: Math.min(highComplexityCount * 20, 80),
                impact: 70,
                category: 'technical',
                mitigation: 'Phased rollout with extensive testing',
                owner: 'Technical Team'
            });
        }
        // Risques business
        const totalEffort = analyses.reduce((sum, a) => sum + a.estimatedEffort, 0);
        if (totalEffort > 100) {
            risks.push({
                id: 'resource_constraint',
                description: 'High resource requirements for implementation',
                probability: 60,
                impact: 50,
                category: 'business',
                mitigation: 'Resource planning and timeline adjustment',
                owner: 'Project Manager'
            });
        }
        // Risques de sécurité
        const securityRisks = analyses.filter(a => a.securityImplications.length > 0);
        if (securityRisks.length > 0) {
            risks.push({
                id: 'security_implications',
                description: 'Security implications from new technologies',
                probability: 40,
                impact: 80,
                category: 'security',
                mitigation: 'Security review and penetration testing',
                owner: 'Security Team'
            });
        }
        // Calcul du risque global
        const overallRisk = this.calculateOverallRisk(risks);
        return {
            overallRisk,
            risks,
            mitigationStrategies: this.generateMitigationStrategies(risks),
            contingencyPlans: this.generateContingencyPlans(risks)
        };
    }
    /**
     * Crée le plan de rollback
     */
    async createRollbackPlan(phases) {
        const steps = [];
        let stepOrder = 1;
        // Création des étapes de rollback en ordre inverse
        for (let i = phases.length - 1; i >= 0; i--) {
            const phase = phases[i];
            for (const agentId of phase.agentsToUpdate) {
                steps.push({
                    id: `rollback_${stepOrder}`,
                    description: `Rollback ${agentId} from ${phase.name}`,
                    action: `docker rollback ${agentId}`,
                    order: stepOrder++,
                    estimatedDuration: 5,
                    dependencies: [],
                    validationCommand: `curl -f http://${agentId}:3000/health`,
                    rollbackCommand: `docker service update --rollback ${agentId}`
                });
            }
        }
        const totalDuration = steps.reduce((sum, step) => sum + step.estimatedDuration, 0);
        return {
            id: `rollback_${Date.now()}`,
            description: 'Complete system rollback plan',
            steps,
            estimatedDuration: totalDuration,
            dataBackupRequired: true,
            triggers: [
                'Critical validation failure',
                'System instability',
                'Security breach',
                'Performance degradation > 50%'
            ],
            approvalRequired: true,
            testPlan: [
                'Verify all services are running',
                'Check data integrity',
                'Validate system performance',
                'Confirm security posture'
            ]
        };
    }
    /**
     * Crée les critères de validation pour une phase
     */
    createValidationCriteria(analyses) {
        const criteria = [];
        // Critères de performance
        criteria.push({
            name: 'Response Time',
            type: 'performance',
            threshold: 200,
            metric: 'ms',
            required: true,
            testCommand: 'curl -w "%{time_total}" http://api/health',
            timeout: 30
        });
        criteria.push({
            name: 'Error Rate',
            type: 'performance',
            threshold: 1,
            metric: '%',
            required: true,
            testCommand: 'check_error_rate.sh',
            timeout: 60
        });
        // Critères de sécurité
        if (analyses.some(a => a.securityImplications.length > 0)) {
            criteria.push({
                name: 'Security Scan',
                type: 'security',
                threshold: 0,
                metric: 'critical_vulnerabilities',
                required: true,
                testCommand: 'security_scan.sh',
                timeout: 300
            });
        }
        // Critères fonctionnels
        criteria.push({
            name: 'Functional Tests',
            type: 'functionality',
            threshold: 95,
            metric: '% passed',
            required: true,
            testCommand: 'npm run test:functional',
            timeout: 600
        });
        return criteria;
    }
    /**
     * Méthodes utilitaires
     */
    hasConflicts(analysis, selected) {
        // Vérification des conflits de dépendances
        for (const selectedAnalysis of selected) {
            if (this.technologiesConflict(analysis.technology, selectedAnalysis.technology)) {
                return true;
            }
        }
        return false;
    }
    technologiesConflict(tech1, tech2) {
        // Logique de détection de conflits entre technologies
        if (tech1.category === tech2.category && tech1.name !== tech2.name) {
            // Même catégorie, technologies différentes = conflit potentiel
            return true;
        }
        return false;
    }
    getUniqueAgents(analyses) {
        const agents = new Set();
        analyses.forEach(a => a.affectedAgents.forEach(agent => agents.add(agent)));
        return Array.from(agents);
    }
    getPhaseDependencies(analyses, previousPhases) {
        if (previousPhases.length === 0)
            return [];
        return [previousPhases[previousPhases.length - 1].id];
    }
    calculatePhaseDuration(analyses) {
        return analyses.reduce((sum, a) => sum + a.estimatedEffort, 0);
    }
    generatePlanId() {
        return `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generatePlanName(analyses) {
        const techNames = analyses.slice(0, 3).map(a => a.technology.name);
        return `Evolution: ${techNames.join(', ')}${analyses.length > 3 ? ` +${analyses.length - 3} more` : ''}`;
    }
    generatePlanDescription(analyses) {
        const totalValue = analyses.reduce((sum, a) => sum + a.businessValue, 0);
        const avgValue = Math.round(totalValue / analyses.length);
        return `Evolution plan for ${analyses.length} technologies with average business value of ${avgValue}%. ` +
            `Estimated total effort: ${analyses.reduce((sum, a) => sum + a.estimatedEffort, 0)} hours.`;
    }
    generatePhaseName(analyses) {
        const complexity = analyses[0]?.migrationComplexity || 'medium';
        const complexityNames = {
            'low': 'Quick Wins',
            'medium': 'Standard Updates',
            'high': 'Complex Migrations',
            'very_high': 'Major Overhauls'
        };
        return complexityNames[complexity];
    }
    generatePhaseDescription(analyses) {
        const techNames = analyses.map(a => a.technology.name).join(', ');
        return `Implementation of ${techNames}`;
    }
    defineRollbackTriggers(analyses) {
        const triggers = ['Validation failure', 'Performance degradation'];
        if (analyses.some(a => a.securityImplications.length > 0)) {
            triggers.push('Security vulnerability detected');
        }
        if (analyses.some(a => a.migrationComplexity === 'very_high')) {
            triggers.push('Critical system instability');
        }
        return triggers;
    }
    definePrerequisites(analyses) {
        const prerequisites = ['System backup completed', 'Team notification sent'];
        if (analyses.some(a => a.estimatedEffort > 20)) {
            prerequisites.push('Extended maintenance window approved');
        }
        return prerequisites;
    }
    defineDeliverables(analyses) {
        return [
            'Updated agent deployments',
            'Validation test results',
            'Performance metrics report',
            'Documentation updates'
        ];
    }
    determinePlanPriority(analyses, riskAssessment) {
        const avgImpactScore = analyses.reduce((sum, a) => sum + a.impactScore, 0) / analyses.length;
        const hasHighRisk = riskAssessment.overallRisk === 'high' || riskAssessment.overallRisk === 'very_high';
        if (avgImpactScore > 80 && !hasHighRisk)
            return 'high';
        if (avgImpactScore > 60)
            return 'medium';
        return 'low';
    }
    identifyStakeholders(analyses) {
        const stakeholders = new Set(['Technical Team', 'Product Owner']);
        if (analyses.some(a => a.securityImplications.length > 0)) {
            stakeholders.add('Security Team');
        }
        if (analyses.some(a => a.businessValue > 70)) {
            stakeholders.add('Business Stakeholders');
        }
        return Array.from(stakeholders);
    }
    defineSuccessCriteria(analyses) {
        return [
            'All validation criteria passed',
            'No critical issues detected',
            'Performance maintained or improved',
            'Zero data loss',
            'All agents operational'
        ];
    }
    calculateOverallRisk(risks) {
        if (risks.length === 0)
            return 'low';
        const avgRisk = risks.reduce((sum, r) => sum + (r.probability * r.impact / 100), 0) / risks.length;
        if (avgRisk > 60)
            return 'very_high';
        if (avgRisk > 40)
            return 'high';
        if (avgRisk > 20)
            return 'medium';
        return 'low';
    }
    generateMitigationStrategies(risks) {
        const strategies = new Set();
        risks.forEach(risk => {
            strategies.add(risk.mitigation);
            if (risk.category === 'technical') {
                strategies.add('Comprehensive testing strategy');
                strategies.add('Gradual rollout approach');
            }
            if (risk.category === 'security') {
                strategies.add('Security audit before deployment');
                strategies.add('Monitoring enhancement');
            }
        });
        return Array.from(strategies);
    }
    generateContingencyPlans(risks) {
        const plans = ['Immediate rollback capability', 'Emergency contact procedures'];
        if (risks.some(r => r.category === 'security')) {
            plans.push('Incident response team activation');
        }
        if (risks.some(r => r.impact > 70)) {
            plans.push('Business continuity plan activation');
        }
        return plans;
    }
    async validatePlan(plan) {
        // Validation de la cohérence du plan
        if (plan.phases.length === 0) {
            throw new Error('Plan must have at least one phase');
        }
        if (plan.totalDuration <= 0) {
            throw new Error('Plan duration must be positive');
        }
        // Validation des dépendances entre phases
        for (let i = 1; i < plan.phases.length; i++) {
            const phase = plan.phases[i];
            const hasValidDependency = phase.dependencies.some(dep => plan.phases.slice(0, i).some(prevPhase => prevPhase.id === dep));
            if (phase.dependencies.length > 0 && !hasValidDependency) {
                throw new Error(`Phase ${phase.name} has invalid dependencies`);
            }
        }
    }
    async loadPlanTemplates() {
        // Chargement des templates de plans prédéfinis
        this.planTemplates.set('frontend_update', {
            name: 'Frontend Technology Update',
            description: 'Standard template for frontend technology updates'
        });
        this.planTemplates.set('backend_migration', {
            name: 'Backend Migration',
            description: 'Template for backend technology migrations'
        });
    }
    async loadPlanHistory() {
        // Chargement de l'historique des plans depuis la base de données
        // Simulation pour le moment
        this.planHistory = [];
    }
    /**
     * Getters
     */
    getPlanHistory() {
        return [...this.planHistory];
    }
    getPlanById(id) {
        return this.planHistory.find(plan => plan.id === id);
    }
}
exports.EvolutionPlanner = EvolutionPlanner;
//# sourceMappingURL=EvolutionPlanner.js.map