/**
 * ImpactAnalyzer - Analyseur d'impact des nouvelles technologies
 * Évalue l'impact potentiel des technologies sur le système et les agents
 */
import { EventEmitter } from 'events';
import { Technology, ImpactAnalysis } from './types';
export declare class ImpactAnalyzer extends EventEmitter {
    private logger;
    private agentCapabilities;
    private systemArchitecture;
    private businessMetrics;
    constructor();
    /**
     * Initialise l'analyseur d'impact
     */
    initialize(): Promise<void>;
    /**
     * Analyse l'impact d'une technologie
     */
    analyze(technology: Technology): Promise<ImpactAnalysis>;
    /**
     * Identifie les agents affectés par une technologie
     */
    private identifyAffectedAgents;
    /**
     * Évalue les bénéfices d'une technologie
     */
    private evaluateBenefits;
    /**
     * Évalue les risques d'une technologie
     */
    private evaluateRisks;
    /**
     * Évalue la complexité de migration
     */
    private assessMigrationComplexity;
    /**
     * Estime l'effort nécessaire
     */
    private estimateEffort;
    /**
     * Évalue la valeur business
     */
    private evaluateBusinessValue;
    /**
     * Évalue la dette technique
     */
    private evaluateTechnicalDebt;
    /**
     * Analyse les implications sécuritaires
     */
    private analyzeSecurityImplications;
    /**
     * Analyse l'impact sur les performances
     */
    private analyzePerformanceImpact;
    /**
     * Identifie les problèmes de compatibilité
     */
    private identifyCompatibilityIssues;
    /**
     * Analyse les dépendances
     */
    private analyzeDependencies;
    /**
     * Identifie les changements cassants
     */
    private identifyBreakingChanges;
    /**
     * Calcule le score d'impact global
     */
    private calculateImpactScore;
    /**
     * Méthodes utilitaires
     */
    private technologyMatchesCapabilities;
    private getAgentSpecificBenefits;
    private getAgentSpecificRisks;
    private getExistingDependencies;
    private getAgentCompatibilityIssues;
    private assessLearningCurve;
    private assessCommunitySupport;
    private assessEcosystemMaturity;
    /**
     * Méthodes de chargement des données
     */
    private loadAgentCapabilities;
    private loadSystemArchitecture;
    private loadBusinessMetrics;
}
//# sourceMappingURL=ImpactAnalyzer.d.ts.map