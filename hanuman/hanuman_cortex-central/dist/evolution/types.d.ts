/**
 * Types pour le système d'évolution continue
 */
export interface Technology {
    id: string;
    name: string;
    category: 'framework' | 'library' | 'tool' | 'language' | 'platform' | 'protocol';
    version: string;
    description: string;
    maturity: 'experimental' | 'alpha' | 'beta' | 'stable' | 'mature' | 'deprecated';
    adoptionRate: number;
    githubStars?: number;
    npmDownloads?: number;
    stackOverflowQuestions?: number;
    jobPostings?: number;
    discoveredAt: Date;
    source: string;
    relevanceScore: number;
    tags: string[];
    documentation?: string;
    license?: string;
    maintainers?: string[];
    lastUpdate?: Date;
    securityScore?: number;
    performanceScore?: number;
}
export interface TechnologySource {
    name: string;
    type: 'github' | 'npm' | 'stackoverflow' | 'techblogs' | 'patents' | 'academic' | 'vc' | 'reddit' | 'hackernews';
    url: string;
    lastScanned: Date;
    isActive: boolean;
    scanInterval: number;
    priority: number;
    filters?: string[];
    credentials?: {
        apiKey?: string;
        token?: string;
    };
}
export interface ImpactAnalysis {
    technology: Technology;
    impactScore: number;
    affectedAgents: string[];
    benefits: string[];
    risks: string[];
    migrationComplexity: 'low' | 'medium' | 'high' | 'very_high';
    estimatedEffort: number;
    businessValue: number;
    technicalDebt: number;
    securityImplications: string[];
    performanceImpact: number;
    compatibilityIssues: string[];
    dependencies: string[];
    breakingChanges: string[];
    learningCurve: number;
    communitySupport: number;
    ecosystemMaturity: number;
}
export interface EvolutionPlan {
    id: string;
    name: string;
    description: string;
    technologies: Technology[];
    phases: EvolutionPhase[];
    totalDuration: number;
    priority: 'low' | 'medium' | 'high' | 'critical';
    status: 'planned' | 'approved' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | 'paused';
    createdAt: Date;
    approvedAt?: Date;
    startedAt?: Date;
    completedAt?: Date;
    rollbackPlan: RollbackPlan;
    budget?: number;
    stakeholders: string[];
    successCriteria: string[];
    riskAssessment: RiskAssessment;
}
export interface EvolutionPhase {
    id: string;
    name: string;
    description: string;
    agentsToUpdate: string[];
    dependencies: string[];
    estimatedDuration: number;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
    startedAt?: Date;
    completedAt?: Date;
    validationCriteria: ValidationCriteria[];
    rollbackTriggers: string[];
    prerequisites: string[];
    deliverables: string[];
}
export interface ValidationCriteria {
    name: string;
    type: 'performance' | 'security' | 'functionality' | 'compatibility' | 'quality';
    threshold: number;
    metric: string;
    required: boolean;
    testCommand?: string;
    expectedResult?: any;
    timeout?: number;
}
export interface RiskAssessment {
    overallRisk: 'low' | 'medium' | 'high' | 'very_high';
    risks: Risk[];
    mitigationStrategies: string[];
    contingencyPlans: string[];
}
export interface Risk {
    id: string;
    description: string;
    probability: number;
    impact: number;
    category: 'technical' | 'business' | 'security' | 'operational';
    mitigation: string;
    owner: string;
}
export interface RollbackPlan {
    id: string;
    description: string;
    steps: RollbackStep[];
    estimatedDuration: number;
    dataBackupRequired: boolean;
    triggers: string[];
    approvalRequired: boolean;
    testPlan: string[];
}
export interface RollbackStep {
    id: string;
    description: string;
    action: string;
    order: number;
    estimatedDuration: number;
    dependencies: string[];
    validationCommand?: string;
    rollbackCommand?: string;
}
export interface TrainingResult {
    agentId: string;
    success: boolean;
    duration: number;
    newCapabilities: string[];
    performanceImprovement: number;
    errors: string[];
    rollbackRequired: boolean;
    trainingData?: any;
    modelVersion?: string;
    accuracy?: number;
    confidence?: number;
}
export interface DeploymentResult {
    planId: string;
    phaseId: string;
    success: boolean;
    duration: number;
    updated: string[];
    failed: string[];
    rollbacks: string[];
    validationResults: ValidationResult[];
    performanceMetrics?: PerformanceMetrics;
    errorLogs?: string[];
}
export interface ValidationResult {
    criteria: ValidationCriteria;
    passed: boolean;
    actualValue: number;
    expectedValue: number;
    message: string;
    timestamp: Date;
    details?: any;
}
export interface PerformanceMetrics {
    responseTime: number;
    throughput: number;
    errorRate: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkLatency: number;
}
export interface EvolutionReport {
    id: string;
    planId: string;
    technologiesEvaluated: number;
    agentsUpdated: number;
    performanceGains: number;
    rollbacks: number;
    duration: number;
    nextEvolutionDate: Date;
    recommendations: string[];
    lessons: string[];
    createdAt: Date;
    successRate: number;
    costSavings?: number;
    businessImpact?: string;
    technicalDebtReduction?: number;
}
export interface EvolutionConfig {
    scanInterval: number;
    maxConcurrentEvolutions: number;
    autoApproveThreshold: number;
    rollbackTimeout: number;
    validationTimeout: number;
    enableAutomaticEvolution: boolean;
    enablePredictiveEvolution: boolean;
    technologySources: TechnologySource[];
    excludedTechnologies: string[];
    priorityKeywords: string[];
    notificationChannels: string[];
    backupRetention: number;
    maxRollbackAttempts: number;
}
export interface EvolutionMetrics {
    totalEvolutions: number;
    successfulEvolutions: number;
    failedEvolutions: number;
    averageDuration: number;
    averagePerformanceGain: number;
    totalRollbacks: number;
    technologiesAdopted: number;
    agentsEvolved: number;
    lastEvolution: Date;
    nextScheduledEvolution: Date;
}
export interface AgentCapability {
    name: string;
    version: string;
    description: string;
    dependencies: string[];
    performance: number;
    stability: number;
    lastUpdated: Date;
    deprecated?: boolean;
    replacedBy?: string;
}
export interface EvolutionEvent {
    id: string;
    type: 'scan_started' | 'technology_discovered' | 'plan_created' | 'evolution_started' | 'phase_completed' | 'evolution_completed' | 'rollback_triggered' | 'validation_failed';
    timestamp: Date;
    planId?: string;
    phaseId?: string;
    agentId?: string;
    technologyId?: string;
    data: any;
    severity: 'info' | 'warning' | 'error' | 'critical';
    message: string;
}
//# sourceMappingURL=types.d.ts.map