import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';

export interface AgentInfo {
  id: string;
  type: string;
  capabilities: string[];
  status: 'online' | 'offline' | 'busy' | 'error';
  lastSeen: Date;
  performance: {
    successRate: number;
    averageResponseTime: number;
    tasksCompleted: number;
  };
  metadata: any;
}

export interface NeuralActivity {
  timestamp: Date;
  type: 'connection' | 'message' | 'decision' | 'learning';
  source: string;
  target?: string;
  data: any;
  intensity: number;
}

export interface NeuralNetworkConfig {
  communication: SynapticCommunication;
  memory: CentralMemory;
  decisionEngine: DecisionEngine;
}

/**
 * Gestionnaire du Réseau Neuronal
 * 
 * Gère les connexions et interactions entre tous les agents
 * du système nerveux distribué
 */
export class NeuralNetworkManager extends EventEmitter {
  private communication: SynapticCommunication;
  private memory: CentralMemory;
  private decisionEngine: DecisionEngine;
  
  private connectedAgents: Map<string, AgentInfo> = new Map();
  private neuralActivity: NeuralActivity[] = [];
  private synapticConnections: Map<string, Set<string>> = new Map();
  private isInitialized: boolean = false;
  private activityCleanupInterval: NodeJS.Timeout;

  constructor(config: NeuralNetworkConfig) {
    super();
    this.communication = config.communication;
    this.memory = config.memory;
    this.decisionEngine = config.decisionEngine;
  }

  /**
   * Initialise le gestionnaire de réseau neuronal
   */
  async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Gestionnaire de Réseau Neuronal...');

      // Configuration des événements de communication
      this.setupCommunicationEvents();

      // Démarrage du monitoring des agents
      this.startAgentMonitoring();

      // Nettoyage périodique de l'activité neuronale
      this.startActivityCleanup();

      // Découverte des agents existants
      await this.discoverExistingAgents();

      this.isInitialized = true;
      logger.info('✅ Gestionnaire de Réseau Neuronal initialisé');

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du réseau neuronal:', error);
      throw error;
    }
  }

  /**
   * Configuration des événements de communication
   */
  private setupCommunicationEvents(): void {
    // Événements d'agents
    this.communication.on('agent-connected', (agentInfo) => {
      this.handleAgentConnection(agentInfo);
    });

    this.communication.on('agent-disconnected', (agentInfo) => {
      this.handleAgentDisconnection(agentInfo);
    });

    this.communication.on('agent-status-update', (data) => {
      this.handleAgentStatusUpdate(data);
    });

    this.communication.on('agent-message', (data) => {
      this.handleAgentMessage(data);
    });

    // Événements synaptiques
    this.communication.on('synaptic-signal', (data) => {
      this.handleSynapticSignal(data);
    });
  }

  /**
   * Gestion de la connexion d'un agent
   */
  private async handleAgentConnection(agentInfo: any): Promise<void> {
    const agent: AgentInfo = {
      id: agentInfo.id,
      type: agentInfo.type,
      capabilities: agentInfo.capabilities || [],
      status: 'online',
      lastSeen: new Date(),
      performance: {
        successRate: 1.0,
        averageResponseTime: 0,
        tasksCompleted: 0
      },
      metadata: agentInfo.metadata || {}
    };

    this.connectedAgents.set(agent.id, agent);

    // Enregistrement de l'activité neuronale
    this.recordNeuralActivity({
      timestamp: new Date(),
      type: 'connection',
      source: agent.id,
      data: { agentType: agent.type, capabilities: agent.capabilities },
      intensity: 0.8
    });

    // Établissement des connexions synaptiques
    await this.establishSynapticConnections(agent);

    // Stockage en mémoire
    await this.memory.storeAgentInfo(agent);

    logger.info(`🔗 Agent connecté: ${agent.id} (${agent.type})`);
    this.emit('agent-connected', agent);
  }

  /**
   * Gestion de la déconnexion d'un agent
   */
  private handleAgentDisconnection(agentInfo: any): void {
    const agent = this.connectedAgents.get(agentInfo.id);
    if (agent) {
      agent.status = 'offline';
      agent.lastSeen = new Date();

      // Enregistrement de l'activité neuronale
      this.recordNeuralActivity({
        timestamp: new Date(),
        type: 'connection',
        source: agent.id,
        data: { event: 'disconnection' },
        intensity: 0.3
      });

      // Suppression des connexions synaptiques
      this.synapticConnections.delete(agent.id);

      logger.warn(`❌ Agent déconnecté: ${agent.id} (${agent.type})`);
      this.emit('agent-disconnected', agent);

      // Suppression après délai
      setTimeout(() => {
        this.connectedAgents.delete(agentInfo.id);
      }, 300000); // 5 minutes
    }
  }

  /**
   * Gestion des mises à jour de statut d'agents
   */
  private async handleAgentStatusUpdate(data: any): Promise<void> {
    const { agentId, status, performance, metadata } = data;
    const agent = this.connectedAgents.get(agentId);

    if (agent) {
      agent.status = status || agent.status;
      agent.lastSeen = new Date();
      
      if (performance) {
        agent.performance = { ...agent.performance, ...performance };
      }
      
      if (metadata) {
        agent.metadata = { ...agent.metadata, ...metadata };
      }

      // Enregistrement de l'activité neuronale
      this.recordNeuralActivity({
        timestamp: new Date(),
        type: 'message',
        source: agentId,
        data: { statusUpdate: status, performance },
        intensity: 0.4
      });

      // Mise à jour en mémoire
      await this.memory.updateAgentInfo(agent);

      this.emit('agent-status-updated', agent);
    }
  }

  /**
   * Gestion des messages d'agents
   */
  private handleAgentMessage(data: any): void {
    const { agentId, message, targetAgent } = data;

    // Enregistrement de l'activité neuronale
    this.recordNeuralActivity({
      timestamp: new Date(),
      type: 'message',
      source: agentId,
      target: targetAgent,
      data: { messageType: message.type, size: JSON.stringify(message).length },
      intensity: 0.6
    });

    this.emit('agent-message', data);
  }

  /**
   * Gestion des signaux synaptiques
   */
  private handleSynapticSignal(data: any): void {
    const { source, target, signal, strength } = data;

    // Enregistrement de l'activité neuronale
    this.recordNeuralActivity({
      timestamp: new Date(),
      type: 'message',
      source,
      target,
      data: { signal, strength },
      intensity: strength || 0.5
    });

    this.emit('synaptic-activity', data);
  }

  /**
   * Établissement des connexions synaptiques pour un agent
   */
  private async establishSynapticConnections(agent: AgentInfo): Promise<void> {
    const connections = new Set<string>();

    // Logique de connexion basée sur les capacités et types d'agents
    for (const [otherId, otherAgent] of this.connectedAgents) {
      if (otherId !== agent.id && this.shouldConnect(agent, otherAgent)) {
        connections.add(otherId);
        
        // Connexion bidirectionnelle
        const otherConnections = this.synapticConnections.get(otherId) || new Set();
        otherConnections.add(agent.id);
        this.synapticConnections.set(otherId, otherConnections);
      }
    }

    this.synapticConnections.set(agent.id, connections);
    logger.info(`🔗 Connexions synaptiques établies pour ${agent.id}: ${connections.size} connexions`);
  }

  /**
   * Détermine si deux agents doivent être connectés
   */
  private shouldConnect(agent1: AgentInfo, agent2: AgentInfo): boolean {
    // Règles de connexion basées sur les types et capacités
    const compatibilityMatrix = {
      'frontend': ['backend', 'uiux', 'qa'],
      'backend': ['frontend', 'devops', 'qa'],
      'uiux': ['frontend', 'qa'],
      'qa': ['frontend', 'backend', 'uiux', 'devops'],
      'devops': ['backend', 'qa'],
      'security': ['backend', 'devops', 'qa'],
      'performance': ['frontend', 'backend', 'devops']
    };

    const agent1Connections = compatibilityMatrix[agent1.type] || [];
    return agent1Connections.includes(agent2.type);
  }

  /**
   * Enregistrement de l'activité neuronale
   */
  private recordNeuralActivity(activity: NeuralActivity): void {
    this.neuralActivity.push(activity);

    // Limitation de la taille du buffer
    if (this.neuralActivity.length > 1000) {
      this.neuralActivity = this.neuralActivity.slice(-500);
    }

    this.emit('neural-activity', activity);
  }

  /**
   * Démarrage du monitoring des agents
   */
  private startAgentMonitoring(): void {
    setInterval(() => {
      this.performAgentHealthCheck();
    }, 30000); // Toutes les 30 secondes
  }

  /**
   * Vérification de santé des agents
   */
  private async performAgentHealthCheck(): Promise<void> {
    const now = new Date();
    const timeoutThreshold = 120000; // 2 minutes

    for (const [agentId, agent] of this.connectedAgents) {
      const timeSinceLastSeen = now.getTime() - agent.lastSeen.getTime();
      
      if (timeSinceLastSeen > timeoutThreshold && agent.status === 'online') {
        logger.warn(`⚠️ Agent ${agentId} non responsif depuis ${Math.round(timeSinceLastSeen / 1000)}s`);
        agent.status = 'error';
        
        this.recordNeuralActivity({
          timestamp: new Date(),
          type: 'connection',
          source: agentId,
          data: { event: 'timeout', timeSinceLastSeen },
          intensity: 0.2
        });

        this.emit('agent-timeout', agent);
      }
    }
  }

  /**
   * Nettoyage périodique de l'activité neuronale
   */
  private startActivityCleanup(): void {
    this.activityCleanupInterval = setInterval(() => {
      const cutoff = new Date(Date.now() - 3600000); // 1 heure
      this.neuralActivity = this.neuralActivity.filter(
        activity => activity.timestamp > cutoff
      );
    }, 300000); // Toutes les 5 minutes
  }

  /**
   * Découverte des agents existants
   */
  private async discoverExistingAgents(): Promise<void> {
    try {
      // Demande de statut à tous les agents connus
      await this.communication.broadcastMessage({
        type: 'status-request',
        from: 'cortex-central',
        timestamp: new Date()
      });

      logger.info('📡 Demande de découverte d\'agents envoyée');
    } catch (error) {
      logger.warn('⚠️ Erreur lors de la découverte d\'agents:', error);
    }
  }

  /**
   * Récupère les agents connectés
   */
  getConnectedAgents(): AgentInfo[] {
    return Array.from(this.connectedAgents.values());
  }

  /**
   * Récupère un agent par ID
   */
  getAgent(agentId: string): AgentInfo | undefined {
    return this.connectedAgents.get(agentId);
  }

  /**
   * Récupère les agents par type
   */
  getAgentsByType(type: string): AgentInfo[] {
    return Array.from(this.connectedAgents.values())
      .filter(agent => agent.type === type);
  }

  /**
   * Récupère les agents par capacité
   */
  getAgentsByCapability(capability: string): AgentInfo[] {
    return Array.from(this.connectedAgents.values())
      .filter(agent => agent.capabilities.includes(capability));
  }

  /**
   * Récupère l'activité neuronale récente
   */
  getNeuralActivity(limit: number = 100): NeuralActivity[] {
    return this.neuralActivity.slice(-limit);
  }

  /**
   * Récupère les connexions synaptiques
   */
  getSynapticConnections(): Map<string, Set<string>> {
    return new Map(this.synapticConnections);
  }

  /**
   * Récupère le statut du réseau neuronal
   */
  getStatus(): any {
    return {
      isInitialized: this.isInitialized,
      connectedAgents: this.connectedAgents.size,
      synapticConnections: this.synapticConnections.size,
      recentActivity: this.neuralActivity.length,
      agentsByType: this.getAgentDistribution(),
      networkHealth: this.calculateNetworkHealth()
    };
  }

  /**
   * Calcule la distribution des agents par type
   */
  private getAgentDistribution(): Record<string, number> {
    const distribution: Record<string, number> = {};
    
    for (const agent of this.connectedAgents.values()) {
      distribution[agent.type] = (distribution[agent.type] || 0) + 1;
    }
    
    return distribution;
  }

  /**
   * Calcule la santé du réseau
   */
  private calculateNetworkHealth(): number {
    const totalAgents = this.connectedAgents.size;
    if (totalAgents === 0) return 0;

    const onlineAgents = Array.from(this.connectedAgents.values())
      .filter(agent => agent.status === 'online').length;

    const avgSuccessRate = Array.from(this.connectedAgents.values())
      .reduce((sum, agent) => sum + agent.performance.successRate, 0) / totalAgents;

    return Math.round(((onlineAgents / totalAgents) * 0.6 + avgSuccessRate * 0.4) * 100);
  }

  /**
   * Arrêt du gestionnaire de réseau neuronal
   */
  async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt du Gestionnaire de Réseau Neuronal...');
    
    if (this.activityCleanupInterval) {
      clearInterval(this.activityCleanupInterval);
    }

    // Notification de déconnexion à tous les agents
    await this.communication.broadcastMessage({
      type: 'cortex-shutdown',
      from: 'cortex-central',
      timestamp: new Date()
    });

    this.connectedAgents.clear();
    this.synapticConnections.clear();
    this.neuralActivity = [];
    this.isInitialized = false;

    logger.info('✅ Gestionnaire de Réseau Neuronal arrêté');
  }
}
