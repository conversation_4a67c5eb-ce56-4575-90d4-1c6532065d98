import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';

export interface LearningConfig {
  memory: CentralMemory;
  decisionEngine: DecisionEngine;
  neuralNetwork: NeuralNetworkManager;
  learningRate?: number;
  adaptationThreshold?: number;
  maxPatterns?: number;
}

export interface LearningPattern {
  id: string;
  type: 'success' | 'failure' | 'optimization' | 'adaptation';
  context: any;
  action: any;
  result: any;
  confidence: number;
  timestamp: Date;
  domain: string;
  metadata: any;
}

export interface AdaptationRule {
  id: string;
  condition: string;
  action: string;
  priority: number;
  successRate: number;
  usageCount: number;
  lastUsed: Date;
}

export interface LearningMetrics {
  totalPatterns: number;
  successfulAdaptations: number;
  failedAdaptations: number;
  averageConfidence: number;
  learningRate: number;
  adaptationAccuracy: number;
  lastLearningEvent: Date;
}

/**
 * Système d'Apprentissage Continu
 *
 * Implémente l'apprentissage automatique et l'adaptation continue
 * du système basé sur les expériences passées et les résultats obtenus.
 */
export class LearningSystem extends EventEmitter {
  private memory: CentralMemory;
  private decisionEngine: DecisionEngine;
  private neuralNetwork: NeuralNetworkManager;
  private config: LearningConfig;
  private isInitialized: boolean = false;

  // Patterns d'apprentissage
  private learningPatterns: Map<string, LearningPattern> = new Map();

  // Règles d'adaptation
  private adaptationRules: Map<string, AdaptationRule> = new Map();

  // Métriques d'apprentissage
  private metrics: LearningMetrics = {
    totalPatterns: 0,
    successfulAdaptations: 0,
    failedAdaptations: 0,
    averageConfidence: 0,
    learningRate: 0.1,
    adaptationAccuracy: 0,
    lastLearningEvent: new Date()
  };

  // Cache des prédictions
  private predictionCache: Map<string, any> = new Map();

  constructor(config: LearningConfig) {
    super();

    this.config = config;
    this.memory = config.memory;
    this.decisionEngine = config.decisionEngine;
    this.neuralNetwork = config.neuralNetwork;

    this.metrics.learningRate = config.learningRate || 0.1;
  }

  /**
   * Initialise le système d'apprentissage
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Système d\'Apprentissage...');

      // Chargement des patterns existants
      await this.loadExistingPatterns();

      // Chargement des règles d'adaptation
      await this.loadAdaptationRules();

      // Configuration des événements d'apprentissage
      this.setupLearningEvents();

      // Démarrage de l'apprentissage continu
      this.startContinuousLearning();

      // Démarrage de l'optimisation des patterns
      this.startPatternOptimization();

      this.isInitialized = true;
      logger.info('✅ Système d\'Apprentissage initialisé avec succès');

      this.emit('learning-system-initialized', {
        timestamp: new Date(),
        patternsLoaded: this.learningPatterns.size,
        rulesLoaded: this.adaptationRules.size
      });

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du Système d\'Apprentissage:', error);
      throw error;
    }
  }

  /**
   * Apprend d'une expérience
   */
  public async learnFromExperience(experience: {
    context: any;
    action: any;
    result: any;
    success: boolean;
    domain: string;
    metadata?: any;
  }): Promise<void> {
    try {
      const pattern: LearningPattern = {
        id: `pattern-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: experience.success ? 'success' : 'failure',
        context: experience.context,
        action: experience.action,
        result: experience.result,
        confidence: this.calculateConfidence(experience),
        timestamp: new Date(),
        domain: experience.domain,
        metadata: experience.metadata || {}
      };

      // Stockage du pattern
      await this.storePattern(pattern);

      // Mise à jour des règles d'adaptation
      await this.updateAdaptationRules(pattern);

      // Mise à jour des métriques
      this.updateLearningMetrics(pattern);

      // Émission d'événement d'apprentissage
      this.emit('pattern-learned', {
        pattern,
        timestamp: new Date()
      });

      logger.info(`📚 Nouveau pattern appris: ${pattern.id} (${pattern.type})`);

    } catch (error) {
      logger.error('Erreur lors de l\'apprentissage:', error);
      throw error;
    }
  }

  /**
   * Prédit le meilleur plan d'action
   */
  public async predictBestAction(context: any, domain: string): Promise<{
    action: any;
    confidence: number;
    reasoning: string;
  }> {
    try {
      // Recherche de patterns similaires
      const similarPatterns = await this.findSimilarPatterns(context, domain);

      if (similarPatterns.length === 0) {
        return {
          action: null,
          confidence: 0,
          reasoning: 'Aucun pattern similaire trouvé'
        };
      }

      // Analyse des patterns de succès
      const successPatterns = similarPatterns.filter(p => p.type === 'success');

      if (successPatterns.length === 0) {
        return {
          action: null,
          confidence: 0,
          reasoning: 'Aucun pattern de succès trouvé'
        };
      }

      // Sélection du meilleur pattern
      const bestPattern = this.selectBestPattern(successPatterns, context);

      return {
        action: bestPattern.action,
        confidence: bestPattern.confidence,
        reasoning: `Basé sur ${successPatterns.length} patterns de succès similaires`
      };

    } catch (error) {
      logger.error('Erreur lors de la prédiction:', error);
      throw error;
    }
  }

  /**
   * Adapte le comportement du système
   */
  public async adaptBehavior(trigger: string, context: any): Promise<boolean> {
    try {
      // Recherche de règles d'adaptation applicables
      const applicableRules = Array.from(this.adaptationRules.values())
        .filter(rule => this.evaluateCondition(rule.condition, context))
        .sort((a, b) => b.priority - a.priority);

      if (applicableRules.length === 0) {
        return false;
      }

      // Application de la meilleure règle
      const bestRule = applicableRules[0];
      const success = await this.applyAdaptationRule(bestRule, context);

      // Mise à jour des statistiques de la règle
      bestRule.usageCount++;
      bestRule.lastUsed = new Date();

      if (success) {
        bestRule.successRate = (bestRule.successRate * (bestRule.usageCount - 1) + 1) / bestRule.usageCount;
        this.metrics.successfulAdaptations++;
      } else {
        bestRule.successRate = (bestRule.successRate * (bestRule.usageCount - 1)) / bestRule.usageCount;
        this.metrics.failedAdaptations++;
      }

      // Mise à jour de la précision d'adaptation
      this.metrics.adaptationAccuracy =
        this.metrics.successfulAdaptations /
        (this.metrics.successfulAdaptations + this.metrics.failedAdaptations);

      this.emit('behavior-adapted', {
        rule: bestRule,
        success,
        context,
        timestamp: new Date()
      });

      return success;

    } catch (error) {
      logger.error('Erreur lors de l\'adaptation:', error);
      return false;
    }
  }

  /**
   * Optimise les patterns d'apprentissage
   */
  public async optimizePatterns(): Promise<void> {
    try {
      logger.info('🔧 Optimisation des patterns d\'apprentissage...');

      // Suppression des patterns obsolètes
      await this.removeObsoletePatterns();

      // Fusion des patterns similaires
      await this.mergeSimilarPatterns();

      // Mise à jour des confidences
      await this.updatePatternConfidences();

      // Nettoyage du cache
      this.predictionCache.clear();

      logger.info('✅ Optimisation des patterns terminée');

    } catch (error) {
      logger.error('Erreur lors de l\'optimisation:', error);
    }
  }

  /**
   * Calcule la confiance d'un pattern
   */
  private calculateConfidence(experience: any): number {
    let confidence = 0.5; // Base

    // Facteurs d'augmentation de confiance
    if (experience.success) confidence += 0.3;
    if (experience.metadata?.executionTime < 1000) confidence += 0.1;
    if (experience.metadata?.resourceUsage < 0.5) confidence += 0.1;

    // Normalisation
    return Math.min(Math.max(confidence, 0), 1);
  }

  /**
   * Stocke un pattern d'apprentissage
   */
  private async storePattern(pattern: LearningPattern): Promise<void> {
    // Stockage en mémoire locale
    this.learningPatterns.set(pattern.id, pattern);

    // Stockage en mémoire centrale
    await this.memory.storeGlobalPattern({
      id: pattern.id,
      content: {
        type: pattern.type,
        context: pattern.context,
        action: pattern.action,
        result: pattern.result
      },
      domain: pattern.domain,
      timestamp: pattern.timestamp,
      version: '1.0',
      metadata: {
        confidence: pattern.confidence,
        learningSystem: true,
        ...pattern.metadata
      }
    });

    this.metrics.totalPatterns++;
  }

  /**
   * Charge les patterns existants
   */
  private async loadExistingPatterns(): Promise<void> {
    try {
      // Chargement depuis la mémoire centrale
      const patterns = await this.memory.searchPatterns({
        domain: '*',
        filters: { learningSystem: true },
        limit: this.config.maxPatterns || 10000
      });

      for (const pattern of patterns) {
        const learningPattern: LearningPattern = {
          id: pattern.id,
          type: pattern.content.type,
          context: pattern.content.context,
          action: pattern.content.action,
          result: pattern.content.result,
          confidence: pattern.metadata.confidence || 0.5,
          timestamp: new Date(pattern.timestamp),
          domain: pattern.domain,
          metadata: pattern.metadata
        };

        this.learningPatterns.set(pattern.id, learningPattern);
      }

      this.metrics.totalPatterns = this.learningPatterns.size;
      logger.info(`📚 ${this.learningPatterns.size} patterns chargés`);

    } catch (error) {
      logger.error('Erreur lors du chargement des patterns:', error);
    }
  }

  /**
   * Trouve des patterns similaires
   */
  private async findSimilarPatterns(context: any, domain: string): Promise<LearningPattern[]> {
    const similarPatterns: LearningPattern[] = [];

    for (const pattern of this.learningPatterns.values()) {
      if (pattern.domain === domain || domain === '*') {
        const similarity = this.calculateSimilarity(context, pattern.context);
        if (similarity > 0.7) { // Seuil de similarité
          similarPatterns.push(pattern);
        }
      }
    }

    return similarPatterns.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Calcule la similarité entre deux contextes
   */
  private calculateSimilarity(context1: any, context2: any): number {
    // Implémentation basique - à améliorer selon les besoins
    const keys1 = Object.keys(context1);
    const keys2 = Object.keys(context2);

    const commonKeys = keys1.filter(key => keys2.includes(key));
    const totalKeys = new Set([...keys1, ...keys2]).size;

    if (totalKeys === 0) return 0;

    let matchingValues = 0;
    for (const key of commonKeys) {
      if (context1[key] === context2[key]) {
        matchingValues++;
      }
    }

    return (commonKeys.length + matchingValues) / (totalKeys + commonKeys.length);
  }

  /**
   * Sélectionne le meilleur pattern
   */
  private selectBestPattern(patterns: LearningPattern[], context: any): LearningPattern {
    return patterns.reduce((best, current) => {
      const currentScore = current.confidence * this.calculateSimilarity(context, current.context);
      const bestScore = best.confidence * this.calculateSimilarity(context, best.context);

      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * Configure les événements d'apprentissage
   */
  private setupLearningEvents(): void {
    // Écoute des événements du système pour apprentissage automatique
    this.decisionEngine.on('decision-made', (data) => {
      this.learnFromDecision(data);
    });

    this.neuralNetwork.on('task-completed', (data) => {
      this.learnFromTaskCompletion(data);
    });
  }

  /**
   * Apprend d'une décision
   */
  private async learnFromDecision(data: any): Promise<void> {
    // Implémentation de l'apprentissage basé sur les décisions
  }

  /**
   * Apprend de l'achèvement d'une tâche
   */
  private async learnFromTaskCompletion(data: any): Promise<void> {
    // Implémentation de l'apprentissage basé sur les tâches
  }

  /**
   * Démarre l'apprentissage continu
   */
  private startContinuousLearning(): void {
    setInterval(async () => {
      await this.performContinuousLearning();
    }, 300000); // Toutes les 5 minutes
  }

  /**
   * Effectue l'apprentissage continu
   */
  private async performContinuousLearning(): Promise<void> {
    try {
      // Analyse des patterns récents
      await this.analyzeRecentPatterns();

      // Mise à jour des règles d'adaptation
      await this.updateAdaptationRulesFromPatterns();

      // Optimisation automatique
      if (this.metrics.totalPatterns > 1000) {
        await this.optimizePatterns();
      }

    } catch (error) {
      logger.error('Erreur lors de l\'apprentissage continu:', error);
    }
  }

  /**
   * Démarre l'optimisation des patterns
   */
  private startPatternOptimization(): void {
    setInterval(async () => {
      await this.optimizePatterns();
    }, 3600000); // Toutes les heures
  }

  /**
   * Met à jour les métriques d'apprentissage
   */
  private updateLearningMetrics(pattern: LearningPattern): void {
    this.metrics.lastLearningEvent = new Date();

    // Calcul de la confiance moyenne
    const totalConfidence = Array.from(this.learningPatterns.values())
      .reduce((sum, p) => sum + p.confidence, 0);

    this.metrics.averageConfidence = totalConfidence / this.learningPatterns.size;
  }

  /**
   * Charge les règles d'adaptation
   */
  private async loadAdaptationRules(): Promise<void> {
    // Règles d'adaptation par défaut
    const defaultRules: AdaptationRule[] = [
      {
        id: 'performance-optimization',
        condition: 'responseTime > 5000',
        action: 'optimize-performance',
        priority: 90,
        successRate: 0.8,
        usageCount: 0,
        lastUsed: new Date()
      },
      {
        id: 'error-recovery',
        condition: 'errorRate > 0.1',
        action: 'activate-recovery',
        priority: 95,
        successRate: 0.9,
        usageCount: 0,
        lastUsed: new Date()
      }
    ];

    for (const rule of defaultRules) {
      this.adaptationRules.set(rule.id, rule);
    }
  }

  /**
   * Met à jour les règles d'adaptation
   */
  private async updateAdaptationRules(pattern: LearningPattern): Promise<void> {
    // Logique de mise à jour des règles basée sur les nouveaux patterns
    if (pattern.type === 'success' && pattern.confidence > 0.8) {
      // Créer ou renforcer une règle d'adaptation
      const ruleId = `auto-rule-${pattern.domain}-${Date.now()}`;

      if (!this.adaptationRules.has(ruleId)) {
        const newRule: AdaptationRule = {
          id: ruleId,
          condition: this.generateConditionFromPattern(pattern),
          action: this.generateActionFromPattern(pattern),
          priority: Math.floor(pattern.confidence * 100),
          successRate: pattern.confidence,
          usageCount: 1,
          lastUsed: new Date()
        };

        this.adaptationRules.set(ruleId, newRule);
      }
    }
  }

  /**
   * Génère une condition à partir d'un pattern
   */
  private generateConditionFromPattern(pattern: LearningPattern): string {
    // Logique simplifiée pour générer des conditions
    return `domain === '${pattern.domain}' && complexity === '${pattern.context.complexity || 'medium'}'`;
  }

  /**
   * Génère une action à partir d'un pattern
   */
  private generateActionFromPattern(pattern: LearningPattern): string {
    // Logique simplifiée pour générer des actions
    return `apply-pattern-${pattern.id}`;
  }

  /**
   * Évalue une condition
   */
  private evaluateCondition(condition: string, context: any): boolean {
    try {
      // Évaluation sécurisée des conditions
      // Dans un environnement de production, utiliser un parser plus sécurisé
      const func = new Function('context', `with(context) { return ${condition}; }`);
      return func(context);
    } catch (error) {
      logger.error('Erreur lors de l\'évaluation de la condition:', error);
      return false;
    }
  }

  /**
   * Applique une règle d'adaptation
   */
  private async applyAdaptationRule(rule: AdaptationRule, context: any): Promise<boolean> {
    try {
      logger.info(`🔧 Application de la règle d'adaptation: ${rule.id}`);

      // Logique d'application des règles
      switch (rule.action) {
        case 'optimize-performance':
          return await this.optimizePerformance(context);
        case 'activate-recovery':
          return await this.activateRecovery(context);
        default:
          logger.warn(`Action inconnue: ${rule.action}`);
          return false;
      }
    } catch (error) {
      logger.error('Erreur lors de l\'application de la règle:', error);
      return false;
    }
  }

  /**
   * Optimise les performances
   */
  private async optimizePerformance(context: any): Promise<boolean> {
    // Implémentation de l'optimisation des performances
    logger.info('🚀 Optimisation des performances en cours...');
    return true;
  }

  /**
   * Active la récupération
   */
  private async activateRecovery(context: any): Promise<boolean> {
    // Implémentation de la récupération
    logger.info('🔄 Activation de la récupération...');
    return true;
  }

  /**
   * Supprime les patterns obsolètes
   */
  private async removeObsoletePatterns(): Promise<void> {
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 jours

    for (const [id, pattern] of this.learningPatterns.entries()) {
      if (pattern.timestamp < cutoffDate && pattern.confidence < 0.3) {
        this.learningPatterns.delete(id);
        this.metrics.totalPatterns--;
      }
    }
  }

  /**
   * Fusionne les patterns similaires
   */
  private async mergeSimilarPatterns(): Promise<void> {
    // Logique de fusion des patterns similaires
    const patterns = Array.from(this.learningPatterns.values());

    for (let i = 0; i < patterns.length; i++) {
      for (let j = i + 1; j < patterns.length; j++) {
        const similarity = this.calculateSimilarity(patterns[i].context, patterns[j].context);

        if (similarity > 0.95 && patterns[i].domain === patterns[j].domain) {
          // Fusionner les patterns
          const mergedPattern = this.mergePatterns(patterns[i], patterns[j]);
          this.learningPatterns.set(mergedPattern.id, mergedPattern);
          this.learningPatterns.delete(patterns[j].id);
          patterns.splice(j, 1);
          j--;
        }
      }
    }
  }

  /**
   * Fusionne deux patterns
   */
  private mergePatterns(pattern1: LearningPattern, pattern2: LearningPattern): LearningPattern {
    return {
      id: pattern1.id,
      type: pattern1.type,
      context: { ...pattern1.context, ...pattern2.context },
      action: pattern1.action,
      result: pattern1.result,
      confidence: (pattern1.confidence + pattern2.confidence) / 2,
      timestamp: new Date(),
      domain: pattern1.domain,
      metadata: { ...pattern1.metadata, merged: true, originalIds: [pattern1.id, pattern2.id] }
    };
  }

  /**
   * Met à jour les confidences des patterns
   */
  private async updatePatternConfidences(): Promise<void> {
    for (const pattern of this.learningPatterns.values()) {
      // Dégrade la confiance des anciens patterns
      const ageInDays = (Date.now() - pattern.timestamp.getTime()) / (24 * 60 * 60 * 1000);
      const ageFactor = Math.max(0.1, 1 - (ageInDays / 365)); // Dégrade sur 1 an

      pattern.confidence *= ageFactor;
    }
  }

  /**
   * Analyse les patterns récents
   */
  private async analyzeRecentPatterns(): Promise<void> {
    const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 heures

    const recentPatterns = Array.from(this.learningPatterns.values())
      .filter(p => p.timestamp > recentDate);

    if (recentPatterns.length > 0) {
      const successRate = recentPatterns.filter(p => p.type === 'success').length / recentPatterns.length;

      this.emit('learning-analysis', {
        recentPatterns: recentPatterns.length,
        successRate,
        timestamp: new Date()
      });
    }
  }

  /**
   * Met à jour les règles d'adaptation à partir des patterns
   */
  private async updateAdaptationRulesFromPatterns(): Promise<void> {
    // Analyse des patterns pour créer de nouvelles règles
    const successPatterns = Array.from(this.learningPatterns.values())
      .filter(p => p.type === 'success' && p.confidence > 0.8);

    // Grouper par domaine
    const domainGroups = new Map<string, LearningPattern[]>();

    for (const pattern of successPatterns) {
      if (!domainGroups.has(pattern.domain)) {
        domainGroups.set(pattern.domain, []);
      }
      domainGroups.get(pattern.domain)!.push(pattern);
    }

    // Créer des règles pour chaque domaine avec suffisamment de patterns
    for (const [domain, patterns] of domainGroups.entries()) {
      if (patterns.length >= 5) { // Seuil minimum
        const avgConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length;

        const ruleId = `domain-rule-${domain}`;
        if (!this.adaptationRules.has(ruleId)) {
          const newRule: AdaptationRule = {
            id: ruleId,
            condition: `domain === '${domain}'`,
            action: `optimize-for-${domain}`,
            priority: Math.floor(avgConfidence * 100),
            successRate: avgConfidence,
            usageCount: 0,
            lastUsed: new Date()
          };

          this.adaptationRules.set(ruleId, newRule);
        }
      }
    }
  }

  /**
   * Obtient les métriques d'apprentissage
   */
  public getMetrics(): LearningMetrics {
    return { ...this.metrics };
  }

  /**
   * Vérifie si le système est initialisé
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}
