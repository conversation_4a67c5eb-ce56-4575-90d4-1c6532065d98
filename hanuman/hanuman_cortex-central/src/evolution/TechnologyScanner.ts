/**
 * TechnologyScanner - Scanner de technologies pour la détection des innovations
 * Surveille multiple sources pour identifier les nouvelles technologies pertinentes
 */

import axios from 'axios';
import { EventEmitter } from 'events';
import { Logger } from '../utils/logger';
import { Technology, TechnologySource } from './types';

export class TechnologyScanner extends EventEmitter {
  private logger: Logger;
  private sources: TechnologySource[];
  private cache: Map<string, Technology[]> = new Map();
  private lastScanResults: Technology[] = [];

  constructor(sources: TechnologySource[]) {
    super();
    this.logger = new Logger('TechnologyScanner');
    this.sources = sources;
  }

  /**
   * Initialise le scanner
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing TechnologyScanner...');
    
    // Validation des sources
    for (const source of this.sources) {
      if (!source.isActive) {
        continue;
      }
      
      try {
        await this.validateSource(source);
        this.logger.info(`Source validated: ${source.name}`);
      } catch (error) {
        this.logger.warn(`Source validation failed for ${source.name}:`, error);
        source.isActive = false;
      }
    }

    this.logger.info(`TechnologyScanner initialized with ${this.sources.filter(s => s.isActive).length} active sources`);
  }

  /**
   * Scan toutes les sources actives
   */
  async scanAll(): Promise<Technology[]> {
    this.logger.info('Starting comprehensive technology scan...');
    
    const allTechnologies: Technology[] = [];
    const scanPromises: Promise<Technology[]>[] = [];

    for (const source of this.sources.filter(s => s.isActive)) {
      scanPromises.push(this.scanSource(source));
    }

    try {
      const results = await Promise.allSettled(scanPromises);
      
      results.forEach((result, index) => {
        const source = this.sources.filter(s => s.isActive)[index];
        
        if (result.status === 'fulfilled') {
          allTechnologies.push(...result.value);
          this.cache.set(source.name, result.value);
          source.lastScanned = new Date();
          this.logger.info(`Scanned ${result.value.length} technologies from ${source.name}`);
        } else {
          this.logger.error(`Failed to scan ${source.name}:`, result.reason);
        }
      });

      // Déduplication et filtrage
      const uniqueTechnologies = this.deduplicateAndFilter(allTechnologies);
      
      // Calcul des scores de pertinence
      const scoredTechnologies = await this.calculateRelevanceScores(uniqueTechnologies);
      
      // Tri par score de pertinence
      const sortedTechnologies = scoredTechnologies.sort((a, b) => b.relevanceScore - a.relevanceScore);
      
      this.lastScanResults = sortedTechnologies;
      this.logger.info(`Technology scan completed: ${sortedTechnologies.length} unique technologies found`);
      
      return sortedTechnologies;

    } catch (error) {
      this.logger.error('Technology scan failed:', error);
      throw error;
    }
  }

  /**
   * Scan une source spécifique
   */
  private async scanSource(source: TechnologySource): Promise<Technology[]> {
    this.logger.debug(`Scanning source: ${source.name}`);
    
    switch (source.type) {
      case 'github':
        return this.scanGitHub(source);
      case 'npm':
        return this.scanNPM(source);
      case 'stackoverflow':
        return this.scanStackOverflow(source);
      case 'techblogs':
        return this.scanTechBlogs(source);
      case 'reddit':
        return this.scanReddit(source);
      case 'hackernews':
        return this.scanHackerNews(source);
      default:
        this.logger.warn(`Unknown source type: ${source.type}`);
        return [];
    }
  }

  /**
   * Scan GitHub pour les projets tendance
   */
  private async scanGitHub(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    try {
      const headers: any = {
        'User-Agent': 'EvolutionEngine/1.0'
      };
      
      if (source.credentials?.token) {
        headers['Authorization'] = `token ${source.credentials.token}`;
      }

      // Recherche des repositories tendance
      const trendingResponse = await axios.get('https://api.github.com/search/repositories', {
        headers,
        params: {
          q: 'created:>2024-01-01 stars:>100',
          sort: 'stars',
          order: 'desc',
          per_page: 50
        }
      });

      for (const repo of trendingResponse.data.items) {
        const technology: Technology = {
          id: `github_${repo.id}`,
          name: repo.name,
          category: this.categorizeFromLanguage(repo.language),
          version: 'latest',
          description: repo.description || '',
          maturity: this.assessMaturityFromGitHub(repo),
          adoptionRate: this.calculateAdoptionRate(repo.stargazers_count, repo.forks_count),
          githubStars: repo.stargazers_count,
          discoveredAt: new Date(),
          source: source.name,
          relevanceScore: 0, // Sera calculé plus tard
          tags: repo.topics || [],
          documentation: repo.homepage || repo.html_url,
          license: repo.license?.name,
          lastUpdate: new Date(repo.updated_at)
        };

        technologies.push(technology);
      }

    } catch (error) {
      this.logger.error(`GitHub scan failed:`, error);
    }

    return technologies;
  }

  /**
   * Scan NPM pour les packages populaires
   */
  private async scanNPM(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    try {
      // Recherche des packages populaires récents
      const searchResponse = await axios.get('https://registry.npmjs.org/-/v1/search', {
        params: {
          text: 'keywords:framework,library,tool',
          size: 50,
          from: 0,
          quality: 0.65,
          popularity: 0.98,
          maintenance: 0.5
        }
      });

      for (const pkg of searchResponse.data.objects) {
        const packageData = pkg.package;
        
        const technology: Technology = {
          id: `npm_${packageData.name}`,
          name: packageData.name,
          category: 'library',
          version: packageData.version,
          description: packageData.description || '',
          maturity: this.assessMaturityFromNPM(packageData),
          adoptionRate: this.calculateNPMAdoptionRate(pkg.score),
          npmDownloads: pkg.score?.detail?.popularity || 0,
          discoveredAt: new Date(),
          source: source.name,
          relevanceScore: 0,
          tags: packageData.keywords || [],
          documentation: packageData.links?.homepage,
          license: packageData.license,
          maintainers: packageData.maintainers?.map((m: any) => m.username) || [],
          lastUpdate: new Date(packageData.date)
        };

        technologies.push(technology);
      }

    } catch (error) {
      this.logger.error(`NPM scan failed:`, error);
    }

    return technologies;
  }

  /**
   * Scan StackOverflow pour les technologies émergentes
   */
  private async scanStackOverflow(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    try {
      // Recherche des tags tendance
      const tagsResponse = await axios.get('https://api.stackexchange.com/2.3/tags', {
        params: {
          order: 'desc',
          sort: 'popular',
          site: 'stackoverflow',
          pagesize: 50,
          filter: 'default'
        }
      });

      for (const tag of tagsResponse.data.items) {
        if (this.isRelevantTag(tag.name)) {
          const technology: Technology = {
            id: `so_${tag.name}`,
            name: tag.name,
            category: this.categorizeFromTag(tag.name),
            version: 'unknown',
            description: `Technology discussed on StackOverflow`,
            maturity: 'stable',
            adoptionRate: Math.min(tag.count / 10000, 1), // Normalisation
            stackOverflowQuestions: tag.count,
            discoveredAt: new Date(),
            source: source.name,
            relevanceScore: 0,
            tags: [tag.name]
          };

          technologies.push(technology);
        }
      }

    } catch (error) {
      this.logger.error(`StackOverflow scan failed:`, error);
    }

    return technologies;
  }

  /**
   * Scan des blogs techniques
   */
  private async scanTechBlogs(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    // Cette méthode nécessiterait une implémentation spécifique
    // pour parser les RSS feeds ou APIs des blogs techniques
    this.logger.info('Tech blogs scanning not yet implemented');
    
    return technologies;
  }

  /**
   * Scan Reddit pour les discussions technologiques
   */
  private async scanReddit(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    try {
      const subreddits = ['programming', 'javascript', 'webdev', 'MachineLearning', 'artificial'];
      
      for (const subreddit of subreddits) {
        const response = await axios.get(`https://www.reddit.com/r/${subreddit}/hot.json`, {
          params: { limit: 25 }
        });

        for (const post of response.data.data.children) {
          const postData = post.data;
          
          // Extraction des technologies mentionnées dans le titre
          const mentionedTechs = this.extractTechnologiesFromText(postData.title);
          
          for (const tech of mentionedTechs) {
            const technology: Technology = {
              id: `reddit_${tech}_${postData.id}`,
              name: tech,
              category: 'tool',
              version: 'unknown',
              description: `Technology mentioned on Reddit: ${postData.title}`,
              maturity: 'unknown',
              adoptionRate: Math.min(postData.score / 1000, 1),
              discoveredAt: new Date(),
              source: source.name,
              relevanceScore: 0,
              tags: [subreddit, 'reddit']
            };

            technologies.push(technology);
          }
        }
      }

    } catch (error) {
      this.logger.error(`Reddit scan failed:`, error);
    }

    return technologies;
  }

  /**
   * Scan Hacker News
   */
  private async scanHackerNews(source: TechnologySource): Promise<Technology[]> {
    const technologies: Technology[] = [];
    
    try {
      // Récupération des top stories
      const topStoriesResponse = await axios.get('https://hacker-news.firebaseio.com/v0/topstories.json');
      const topStories = topStoriesResponse.data.slice(0, 50);

      for (const storyId of topStories) {
        const storyResponse = await axios.get(`https://hacker-news.firebaseio.com/v0/item/${storyId}.json`);
        const story = storyResponse.data;

        if (story.title) {
          const mentionedTechs = this.extractTechnologiesFromText(story.title);
          
          for (const tech of mentionedTechs) {
            const technology: Technology = {
              id: `hn_${tech}_${storyId}`,
              name: tech,
              category: 'tool',
              version: 'unknown',
              description: `Technology mentioned on Hacker News: ${story.title}`,
              maturity: 'unknown',
              adoptionRate: Math.min(story.score / 500, 1),
              discoveredAt: new Date(),
              source: source.name,
              relevanceScore: 0,
              tags: ['hackernews']
            };

            technologies.push(technology);
          }
        }
      }

    } catch (error) {
      this.logger.error(`Hacker News scan failed:`, error);
    }

    return technologies;
  }

  /**
   * Validation d'une source
   */
  private async validateSource(source: TechnologySource): Promise<void> {
    try {
      const response = await axios.head(source.url, { timeout: 5000 });
      if (response.status >= 400) {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      throw new Error(`Source validation failed: ${error.message}`);
    }
  }

  /**
   * Déduplication et filtrage des technologies
   */
  private deduplicateAndFilter(technologies: Technology[]): Technology[] {
    const seen = new Set<string>();
    const unique: Technology[] = [];

    for (const tech of technologies) {
      const key = `${tech.name.toLowerCase()}_${tech.category}`;
      
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(tech);
      }
    }

    return unique.filter(tech => this.isRelevantTechnology(tech));
  }

  /**
   * Calcul des scores de pertinence
   */
  private async calculateRelevanceScores(technologies: Technology[]): Promise<Technology[]> {
    return technologies.map(tech => {
      let score = 0;

      // Score basé sur la popularité
      if (tech.githubStars) {
        score += Math.min(tech.githubStars / 1000, 50);
      }
      
      if (tech.npmDownloads) {
        score += Math.min(tech.npmDownloads * 100, 30);
      }
      
      if (tech.stackOverflowQuestions) {
        score += Math.min(tech.stackOverflowQuestions / 100, 20);
      }

      // Score basé sur la maturité
      const maturityScores = {
        'experimental': 5,
        'alpha': 10,
        'beta': 15,
        'stable': 25,
        'mature': 20,
        'deprecated': 0
      };
      score += maturityScores[tech.maturity] || 10;

      // Score basé sur la catégorie
      const categoryScores = {
        'framework': 25,
        'library': 20,
        'tool': 15,
        'language': 30,
        'platform': 25,
        'protocol': 20
      };
      score += categoryScores[tech.category] || 10;

      // Score basé sur la récence
      const daysSinceDiscovery = (Date.now() - tech.discoveredAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceDiscovery < 30) {
        score += 15;
      } else if (daysSinceDiscovery < 90) {
        score += 10;
      }

      tech.relevanceScore = Math.round(score);
      return tech;
    });
  }

  /**
   * Méthodes utilitaires
   */
  private categorizeFromLanguage(language: string): Technology['category'] {
    const languageMap: Record<string, Technology['category']> = {
      'JavaScript': 'library',
      'TypeScript': 'library',
      'Python': 'library',
      'Go': 'tool',
      'Rust': 'tool',
      'Java': 'library',
      'C++': 'tool',
      'Swift': 'language',
      'Kotlin': 'language'
    };
    
    return languageMap[language] || 'tool';
  }

  private categorizeFromTag(tag: string): Technology['category'] {
    if (tag.includes('framework')) return 'framework';
    if (tag.includes('library')) return 'library';
    if (tag.includes('language')) return 'language';
    if (tag.includes('platform')) return 'platform';
    return 'tool';
  }

  private assessMaturityFromGitHub(repo: any): Technology['maturity'] {
    const age = Date.now() - new Date(repo.created_at).getTime();
    const ageInDays = age / (1000 * 60 * 60 * 24);
    
    if (ageInDays < 30) return 'experimental';
    if (ageInDays < 180) return 'alpha';
    if (ageInDays < 365) return 'beta';
    if (repo.stargazers_count > 1000) return 'stable';
    return 'mature';
  }

  private assessMaturityFromNPM(pkg: any): Technology['maturity'] {
    const version = pkg.version;
    if (version.startsWith('0.0')) return 'experimental';
    if (version.startsWith('0.')) return 'alpha';
    if (version.startsWith('1.')) return 'stable';
    return 'mature';
  }

  private calculateAdoptionRate(stars: number, forks: number): number {
    return Math.min((stars + forks * 2) / 10000, 1);
  }

  private calculateNPMAdoptionRate(score: any): number {
    return score?.final || 0;
  }

  private isRelevantTag(tag: string): boolean {
    const relevantTags = [
      'javascript', 'typescript', 'react', 'vue', 'angular', 'node.js',
      'python', 'django', 'flask', 'fastapi', 'machine-learning',
      'artificial-intelligence', 'docker', 'kubernetes', 'aws', 'azure',
      'microservices', 'graphql', 'rest', 'api', 'database'
    ];
    
    return relevantTags.includes(tag.toLowerCase());
  }

  private isRelevantTechnology(tech: Technology): boolean {
    // Filtrage basé sur des critères de pertinence
    if (tech.name.length < 2) return false;
    if (tech.description.length < 10) return false;
    
    // Exclusion des technologies obsolètes
    const obsoleteTechs = ['flash', 'silverlight', 'internet-explorer'];
    if (obsoleteTechs.some(obsolete => tech.name.toLowerCase().includes(obsolete))) {
      return false;
    }
    
    return true;
  }

  private extractTechnologiesFromText(text: string): string[] {
    const techKeywords = [
      'React', 'Vue', 'Angular', 'Node.js', 'Python', 'JavaScript', 'TypeScript',
      'Docker', 'Kubernetes', 'AWS', 'Azure', 'GraphQL', 'REST', 'API',
      'Machine Learning', 'AI', 'Blockchain', 'WebAssembly', 'Rust', 'Go'
    ];
    
    const found: string[] = [];
    const lowerText = text.toLowerCase();
    
    for (const keyword of techKeywords) {
      if (lowerText.includes(keyword.toLowerCase())) {
        found.push(keyword);
      }
    }
    
    return found;
  }

  /**
   * Getters
   */
  public getLastScanResults(): Technology[] {
    return [...this.lastScanResults];
  }

  public getCachedResults(sourceName: string): Technology[] | undefined {
    return this.cache.get(sourceName);
  }

  public getActiveSources(): TechnologySource[] {
    return this.sources.filter(s => s.isActive);
  }
}
