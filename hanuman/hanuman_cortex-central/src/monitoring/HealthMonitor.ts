import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { CortexCentral } from '../core/CortexCentral';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';

export interface HealthMetrics {
  timestamp: Date;
  overall: 'healthy' | 'degraded' | 'critical';
  score: number; // 0-100
  components: {
    cortex: ComponentHealth;
    neuralNetwork: ComponentHealth;
    memory: ComponentHealth;
    agents: ComponentHealth;
    workflows: ComponentHealth;
  };
  alerts: HealthAlert[];
  recommendations: string[];
}

export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  score: number;
  metrics: Record<string, any>;
  lastCheck: Date;
  issues: string[];
}

export interface HealthAlert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  component: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
  metadata?: any;
}

export interface HealthConfig {
  cortex: CortexCentral;
  neuralNetwork: NeuralNetworkManager;
  memory: CentralMemory;
  checkInterval?: number;
  alertThresholds?: {
    cpu: number;
    memory: number;
    responseTime: number;
    errorRate: number;
  };
}

/**
 * Moniteur de Santé du Système
 * 
 * Surveille en continu la santé de tous les composants
 * du système nerveux distribué
 */
export class HealthMonitor extends EventEmitter {
  private cortex: CortexCentral;
  private neuralNetwork: NeuralNetworkManager;
  private memory: CentralMemory;
  
  private checkInterval: number;
  private alertThresholds: any;
  private monitoringInterval: NodeJS.Timeout;
  private healthHistory: HealthMetrics[] = [];
  private activeAlerts: Map<string, HealthAlert> = new Map();
  private isInitialized: boolean = false;

  constructor(config: HealthConfig) {
    super();
    
    this.cortex = config.cortex;
    this.neuralNetwork = config.neuralNetwork;
    this.memory = config.memory;
    this.checkInterval = config.checkInterval || 30000; // 30 secondes
    
    this.alertThresholds = {
      cpu: 80,
      memory: 85,
      responseTime: 5000,
      errorRate: 0.1,
      ...config.alertThresholds
    };
  }

  /**
   * Initialise le moniteur de santé
   */
  async initialize(): Promise<void> {
    try {
      logger.info('🏥 Initialisation du Moniteur de Santé...');

      // Première vérification de santé
      await this.performHealthCheck();

      // Démarrage du monitoring périodique
      this.startPeriodicMonitoring();

      this.isInitialized = true;
      logger.info('✅ Moniteur de Santé initialisé');

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du moniteur de santé:', error);
      throw error;
    }
  }

  /**
   * Démarre le monitoring périodique
   */
  private startPeriodicMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('❌ Erreur lors de la vérification de santé:', error);
      }
    }, this.checkInterval);
  }

  /**
   * Effectue une vérification complète de santé
   */
  async performHealthCheck(): Promise<HealthMetrics> {
    const timestamp = new Date();
    
    // Vérification de chaque composant
    const cortexHealth = await this.checkCortexHealth();
    const neuralHealth = await this.checkNeuralNetworkHealth();
    const memoryHealth = await this.checkMemoryHealth();
    const agentsHealth = await this.checkAgentsHealth();
    const workflowsHealth = await this.checkWorkflowsHealth();

    // Calcul du score global
    const components = {
      cortex: cortexHealth,
      neuralNetwork: neuralHealth,
      memory: memoryHealth,
      agents: agentsHealth,
      workflows: workflowsHealth
    };

    const overallScore = this.calculateOverallScore(components);
    const overallStatus = this.determineOverallStatus(overallScore);

    // Génération des alertes
    const alerts = this.generateAlerts(components);
    
    // Génération des recommandations
    const recommendations = this.generateRecommendations(components);

    const healthMetrics: HealthMetrics = {
      timestamp,
      overall: overallStatus,
      score: overallScore,
      components,
      alerts,
      recommendations
    };

    // Stockage de l'historique
    this.healthHistory.push(healthMetrics);
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }

    // Émission d'événements
    this.emit('health-check-completed', healthMetrics);
    
    if (overallStatus === 'critical') {
      this.emit('system-critical', healthMetrics);
    } else if (overallStatus === 'degraded') {
      this.emit('system-degraded', healthMetrics);
    }

    return healthMetrics;
  }

  /**
   * Vérifie la santé du Cortex Central
   */
  private async checkCortexHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: Record<string, any> = {};

    try {
      const cortexStatus = this.cortex.getStatus();
      
      metrics.isInitialized = cortexStatus.isInitialized;
      metrics.activeTasks = cortexStatus.activeTasks;
      metrics.uptime = cortexStatus.uptime;

      // Vérifications
      if (!cortexStatus.isInitialized) {
        issues.push('Cortex non initialisé');
      }

      if (cortexStatus.activeTasks > 50) {
        issues.push('Nombre élevé de tâches actives');
      }

      const score = this.calculateComponentScore(issues, [
        { condition: cortexStatus.isInitialized, weight: 40 },
        { condition: cortexStatus.activeTasks < 50, weight: 30 },
        { condition: cortexStatus.uptime > 60000, weight: 30 }
      ]);

      return {
        status: this.getStatusFromScore(score),
        score,
        metrics,
        lastCheck: new Date(),
        issues
      };

    } catch (error) {
      issues.push(`Erreur de vérification: ${error.message}`);
      return {
        status: 'critical',
        score: 0,
        metrics,
        lastCheck: new Date(),
        issues
      };
    }
  }

  /**
   * Vérifie la santé du réseau neuronal
   */
  private async checkNeuralNetworkHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: Record<string, any> = {};

    try {
      const neuralStatus = this.neuralNetwork.getStatus();
      const connectedAgents = this.neuralNetwork.getConnectedAgents();
      
      metrics.connectedAgents = neuralStatus.connectedAgents;
      metrics.synapticConnections = neuralStatus.synapticConnections;
      metrics.networkHealth = neuralStatus.networkHealth;
      metrics.recentActivity = neuralStatus.recentActivity;

      // Vérifications
      if (connectedAgents.length === 0) {
        issues.push('Aucun agent connecté');
      }

      if (neuralStatus.networkHealth < 70) {
        issues.push('Santé du réseau dégradée');
      }

      const onlineAgents = connectedAgents.filter(a => a.status === 'online').length;
      const agentHealthRatio = connectedAgents.length > 0 ? onlineAgents / connectedAgents.length : 0;
      
      if (agentHealthRatio < 0.8) {
        issues.push('Trop d\'agents hors ligne');
      }

      const score = this.calculateComponentScore(issues, [
        { condition: connectedAgents.length > 0, weight: 30 },
        { condition: neuralStatus.networkHealth >= 70, weight: 40 },
        { condition: agentHealthRatio >= 0.8, weight: 30 }
      ]);

      return {
        status: this.getStatusFromScore(score),
        score,
        metrics,
        lastCheck: new Date(),
        issues
      };

    } catch (error) {
      issues.push(`Erreur de vérification: ${error.message}`);
      return {
        status: 'critical',
        score: 0,
        metrics,
        lastCheck: new Date(),
        issues
      };
    }
  }

  /**
   * Vérifie la santé de la mémoire centrale
   */
  private async checkMemoryHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: Record<string, any> = {};

    try {
      // Métriques de mémoire système
      const memUsage = process.memoryUsage();
      metrics.heapUsed = Math.round(memUsage.heapUsed / 1024 / 1024);
      metrics.heapTotal = Math.round(memUsage.heapTotal / 1024 / 1024);
      metrics.external = Math.round(memUsage.external / 1024 / 1024);
      metrics.rss = Math.round(memUsage.rss / 1024 / 1024);

      const heapUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      metrics.heapUsagePercent = Math.round(heapUsagePercent);

      // Vérifications
      if (heapUsagePercent > this.alertThresholds.memory) {
        issues.push(`Utilisation mémoire élevée: ${Math.round(heapUsagePercent)}%`);
      }

      if (memUsage.heapUsed > 1024 * 1024 * 1024) { // 1GB
        issues.push('Consommation mémoire très élevée');
      }

      // Vérification de la connectivité Weaviate/Redis
      try {
        const memoryStatus = await this.memory.getStatus();
        metrics.weaviateConnected = memoryStatus.weaviate?.connected || false;
        metrics.redisConnected = memoryStatus.redis?.connected || false;

        if (!metrics.weaviateConnected) {
          issues.push('Weaviate déconnecté');
        }
        if (!metrics.redisConnected) {
          issues.push('Redis déconnecté');
        }
      } catch (error) {
        issues.push('Impossible de vérifier les connexions mémoire');
      }

      const score = this.calculateComponentScore(issues, [
        { condition: heapUsagePercent < this.alertThresholds.memory, weight: 30 },
        { condition: metrics.weaviateConnected, weight: 35 },
        { condition: metrics.redisConnected, weight: 35 }
      ]);

      return {
        status: this.getStatusFromScore(score),
        score,
        metrics,
        lastCheck: new Date(),
        issues
      };

    } catch (error) {
      issues.push(`Erreur de vérification: ${error.message}`);
      return {
        status: 'critical',
        score: 0,
        metrics,
        lastCheck: new Date(),
        issues
      };
    }
  }

  /**
   * Vérifie la santé des agents
   */
  private async checkAgentsHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: Record<string, any> = {};

    try {
      const agents = this.neuralNetwork.getConnectedAgents();
      
      metrics.totalAgents = agents.length;
      metrics.onlineAgents = agents.filter(a => a.status === 'online').length;
      metrics.busyAgents = agents.filter(a => a.status === 'busy').length;
      metrics.errorAgents = agents.filter(a => a.status === 'error').length;

      // Calcul des métriques de performance
      if (agents.length > 0) {
        const avgSuccessRate = agents.reduce((sum, a) => sum + a.performance.successRate, 0) / agents.length;
        const avgResponseTime = agents.reduce((sum, a) => sum + a.performance.averageResponseTime, 0) / agents.length;
        
        metrics.averageSuccessRate = Math.round(avgSuccessRate * 100) / 100;
        metrics.averageResponseTime = Math.round(avgResponseTime);
        metrics.totalTasksCompleted = agents.reduce((sum, a) => sum + a.performance.tasksCompleted, 0);

        // Vérifications
        if (avgSuccessRate < 0.9) {
          issues.push(`Taux de succès faible: ${Math.round(avgSuccessRate * 100)}%`);
        }

        if (avgResponseTime > this.alertThresholds.responseTime) {
          issues.push(`Temps de réponse élevé: ${Math.round(avgResponseTime)}ms`);
        }
      }

      const healthyAgentsRatio = agents.length > 0 ? metrics.onlineAgents / agents.length : 0;
      
      if (agents.length === 0) {
        issues.push('Aucun agent disponible');
      } else if (healthyAgentsRatio < 0.7) {
        issues.push('Trop d\'agents indisponibles');
      }

      const score = this.calculateComponentScore(issues, [
        { condition: agents.length > 0, weight: 25 },
        { condition: healthyAgentsRatio >= 0.7, weight: 25 },
        { condition: (metrics.averageSuccessRate || 0) >= 0.9, weight: 25 },
        { condition: (metrics.averageResponseTime || 0) <= this.alertThresholds.responseTime, weight: 25 }
      ]);

      return {
        status: this.getStatusFromScore(score),
        score,
        metrics,
        lastCheck: new Date(),
        issues
      };

    } catch (error) {
      issues.push(`Erreur de vérification: ${error.message}`);
      return {
        status: 'critical',
        score: 0,
        metrics,
        lastCheck: new Date(),
        issues
      };
    }
  }

  /**
   * Vérifie la santé des workflows
   */
  private async checkWorkflowsHealth(): Promise<ComponentHealth> {
    const issues: string[] = [];
    const metrics: Record<string, any> = {};

    try {
      // Note: Cette méthode nécessiterait l'accès au WorkflowOrchestrator
      // Pour l'instant, on simule les métriques
      metrics.activeWorkflows = 0;
      metrics.queuedWorkflows = 0;
      metrics.completedWorkflows = 0;
      metrics.failedWorkflows = 0;

      // Simulation de vérifications
      const score = 100; // Score parfait en simulation

      return {
        status: this.getStatusFromScore(score),
        score,
        metrics,
        lastCheck: new Date(),
        issues
      };

    } catch (error) {
      issues.push(`Erreur de vérification: ${error.message}`);
      return {
        status: 'critical',
        score: 0,
        metrics,
        lastCheck: new Date(),
        issues
      };
    }
  }

  /**
   * Calcule le score d'un composant
   */
  private calculateComponentScore(issues: string[], conditions: Array<{ condition: boolean; weight: number }>): number {
    if (issues.length > 0) {
      // Pénalité pour chaque problème
      const penalty = Math.min(issues.length * 20, 80);
      return Math.max(0, 100 - penalty);
    }

    const totalWeight = conditions.reduce((sum, c) => sum + c.weight, 0);
    const achievedWeight = conditions
      .filter(c => c.condition)
      .reduce((sum, c) => sum + c.weight, 0);

    return totalWeight > 0 ? Math.round((achievedWeight / totalWeight) * 100) : 0;
  }

  /**
   * Détermine le statut à partir du score
   */
  private getStatusFromScore(score: number): ComponentHealth['status'] {
    if (score >= 80) return 'healthy';
    if (score >= 60) return 'degraded';
    if (score > 0) return 'critical';
    return 'offline';
  }

  /**
   * Calcule le score global
   */
  private calculateOverallScore(components: HealthMetrics['components']): number {
    const weights = {
      cortex: 25,
      neuralNetwork: 25,
      memory: 20,
      agents: 20,
      workflows: 10
    };

    let totalScore = 0;
    let totalWeight = 0;

    for (const [component, health] of Object.entries(components)) {
      const weight = weights[component as keyof typeof weights] || 0;
      totalScore += health.score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  /**
   * Détermine le statut global
   */
  private determineOverallStatus(score: number): HealthMetrics['overall'] {
    if (score >= 80) return 'healthy';
    if (score >= 60) return 'degraded';
    return 'critical';
  }

  /**
   * Génère les alertes basées sur l'état des composants
   */
  private generateAlerts(components: HealthMetrics['components']): HealthAlert[] {
    const alerts: HealthAlert[] = [];

    for (const [componentName, health] of Object.entries(components)) {
      if (health.status === 'critical' || health.status === 'offline') {
        for (const issue of health.issues) {
          const alertId = `${componentName}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
          
          const alert: HealthAlert = {
            id: alertId,
            severity: health.status === 'offline' ? 'critical' : 'high',
            component: componentName,
            message: issue,
            timestamp: new Date(),
            resolved: false,
            metadata: { score: health.score, metrics: health.metrics }
          };

          alerts.push(alert);
          this.activeAlerts.set(alertId, alert);
        }
      }
    }

    return alerts;
  }

  /**
   * Génère des recommandations
   */
  private generateRecommendations(components: HealthMetrics['components']): string[] {
    const recommendations: string[] = [];

    // Recommandations basées sur l'état des composants
    if (components.memory.score < 70) {
      recommendations.push('Considérer l\'augmentation de la mémoire allouée');
    }

    if (components.agents.score < 70) {
      recommendations.push('Vérifier la connectivité des agents');
    }

    if (components.neuralNetwork.score < 70) {
      recommendations.push('Redémarrer les connexions synaptiques');
    }

    if (recommendations.length === 0) {
      recommendations.push('Système en bon état, aucune action requise');
    }

    return recommendations;
  }

  /**
   * Récupère le statut de santé actuel
   */
  getHealthStatus(): HealthMetrics | null {
    return this.healthHistory.length > 0 ? this.healthHistory[this.healthHistory.length - 1] : null;
  }

  /**
   * Récupère l'historique de santé
   */
  getHealthHistory(limit: number = 50): HealthMetrics[] {
    return this.healthHistory.slice(-limit);
  }

  /**
   * Récupère les alertes actives
   */
  getActiveAlerts(): HealthAlert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Résout une alerte
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      this.emit('alert-resolved', alert);
      return true;
    }
    return false;
  }

  /**
   * Arrêt du moniteur de santé
   */
  async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt du Moniteur de Santé...');
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.isInitialized = false;
    logger.info('✅ Moniteur de Santé arrêté');
  }
}
