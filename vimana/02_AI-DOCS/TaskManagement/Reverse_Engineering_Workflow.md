# Reverse Engineering Workflow

This document outlines the process for analyzing existing projects and integrating them into the Agentic Coding Framework.

## Workflow Overview

1. **Initial Analysis**
   - Scan project directory structure
   - Identify main technology stack components
   - Determine project architecture and patterns

2. **Code Analysis**
   - Extract data models and schemas
   - Map API endpoints and services
   - Identify UI components and user flows
   - Document business logic and algorithms

3. **Documentation Reconstruction**
   - Generate technical specifications from code
   - Create architecture diagrams
   - Reverse-engineer PRD from implemented features
   - Formulate core concept and market positioning

4. **Task Management Integration**
   - Create epics for existing feature groups
   - Document completed tasks with references to existing code
   - Generate pending tasks for incomplete features
   - Identify potential improvements and optimizations

5. **Testing and Deployment Analysis**
   - Document existing test coverage
   - Identify testing gaps
   - Map deployment processes if present
   - Recommend CI/CD improvements if needed

## Implementation Guidelines

When performing reverse engineering, follow these principles:

1. **Preserve Existing Patterns:** Maintain consistency with the project's established coding style, naming conventions, and architectural decisions.

2. **Document Assumptions:** Clearly mark inferences about project intent or market positioning as assumptions that should be validated with stakeholders.

3. **Prioritize Clarity:** Focus on creating clear, comprehensive documentation that will enable seamless continuation of development.

4. **Identify Technical Debt:** Note areas of the codebase that may benefit from refactoring or modernization.

5. **Respect Original Design:** Avoid suggesting wholesale architectural changes unless absolutely necessary; instead, work within the established patterns.

## Integration with Roo Orchestrator

When integrating reverse-engineered projects with Roo:

1. Create a special "Existing Features" epic category
2. Mark tasks as "completed" with references to existing code
3. Generate "improvement" tasks for potential optimizations
4. Create standard task hierarchies for new features

## Output Documents

The reverse engineering process should produce:

1. All standard framework documents (`idea_document.md`, `market_research.md`, `core_concept.md`, `project_prd.md`)
2. Populated technical specifications in `02_AI-DOCS/` and `03_SPECS/`
3. A comprehensive `tasks.json` file with both completed and pending tasks
4. A project status report highlighting strengths, gaps, and recommendations