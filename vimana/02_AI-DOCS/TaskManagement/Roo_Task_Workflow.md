# Roo Task Workflow: A Comprehensive Guide
### Leveraging <PERSON><PERSON>'s Orchestration and Coding Capabilities for Task Management

## Overview
Roo, as your integrated AI assistant, handles task management directly. No separate installation or external tools are required. This document outlines how to guide Roo Orchestrator through the project task definition process. Roo Orchestrator will manage the overall workflow and may leverage Roo Code mode for specific technical task detailing or code stub generation.

## Context Awareness

**Previous Phases:**
- Idea Document (logically generated by `01_Idea.md` and saved as `idea_document.md`)
- Market Research (logically generated by `02_Market_Research.md` and saved as `market_research.md`)
- Core Concept (logically generated by `03_Core_Concept.md` and saved as `core_concept.md`)
- PRD Generation (logically generated by `04_PRD_Generation.md` and saved as `project_prd.md`)
- Technical Specifications (project-specific documents **created** in `02_AI-DOCS/` and `03_SPECS/` from templates by the `05_Specs_Docs.md` logical prompt)

**Expected Inputs:**
- A comprehensive `project_prd.md`.
- **Created** project-specific technical specifications and documentation within `02_AI-DOCS/` (including the created [`../Conventions/coding_conventions.md`](../Conventions/coding_conventions.md:1) and [`../Conventions/design_conventions.md`](../Conventions/design_conventions.md:1)) and `../../03_SPECS/`.
- Foundational design principles outlined in [`../Documentation/AI_Design_Agent_Optimization.md`](../Documentation/AI_Design_Agent_Optimization.md:1).
- Coding best practices from [`../Documentation/AI_Coding_Agent_Optimization.md`](../Documentation/AI_Coding_Agent_Optimization.md:1).

**Current Phase:** Task Management

## Purpose

This document establishes a structured workflow for breaking down the PRD into a hierarchical task system using Roo Orchestrator. Roo Orchestrator will analyze the project requirements and, where appropriate, delegate detailed technical task generation or code stub creation to Roo Code mode. The goal is to create extremely precise, granular tasks that are optimally sized for implementation, avoiding overly large or ambiguous work items.

### Advanced Concepts and Vision for AI Task Management

For a deeper dive into advanced AI-driven task management systems and a vision for future capabilities, refer to the **Product Requirements Document for Vibe-Task**. This document, available at [`../../02_AI-DOCS/Documentation/AI_Task_Management_Optimization.md`](../../02_AI-DOCS/Documentation/AI_Task_Management_Optimization.md), serves as a detailed example and conceptual blueprint.

The Vibe-Task PRD can be used as a source of inspiration for:
- Optimizing the current Roo-based task workflow.
- Understanding best practices for agentic task management.
- Exploring potential future capabilities and more sophisticated AI integrations in task planning, decomposition, and execution.

## When to Use

Execute this prompt after the PRD has been fully generated and validated. The task decomposition process requires a complete PRD with well-defined features in Section 3.1.

## Guiding Roo Orchestrator for Task Management

To initiate task management, provide Roo Orchestrator with the necessary project details. Here’s an example of how you might phrase your request:

## Project Initialization and Task Decomposition Request

I need comprehensive task management setup for this project, with features broken down into precise, implementable units of work. The PRD is complete and ready for task decomposition.

### Project Context

- **Project Name:** {{project_name}}
- **PRD Reference:** {{prd_reference_id}}
- **Project Objective:** {{project_objective}}
- **Roo's Approach:** Roo will utilize its configured capabilities and modes (Orchestrator, Code, etc.) to perform these tasks effectively.

### Primary Request

Please perform the following operations in sequence:

1. Initialize project tracking
2. Create epics from the feature list
3. Decompose each feature into a hierarchy of precisely defined tasks (Roo Orchestrator may engage Roo Code mode for technical specifics).
4. Analyze implementation complexity
5. Generate a detailed implementation roadmap

### Feature List for Epic Creation

{{features_json}}

### Decomposition Guidelines

- Break down each feature into tasks that represent no more than 4 hours of development work
- Ensure each task has a single, clear responsibility
- Include specific technical requirements in task descriptions.
- **Crucially, ensure the `details` field of each task (as defined in [`./Tasks_JSON_Structure.md`](./Tasks_JSON_Structure.md:1)) contains direct links to, or embeds content from, the specific, relevant *created* specification documents. This includes:**
   - Relevant sections of `project_prd.md`.
   - Specific feature specifications (e.g., `../03_SPECS/features/feature_spec_FEAT-XXX.md`).
   - Architectural diagrams or guidelines from the created `../02_AI-DOCS/Architecture/architecture.md`.
   - For UI tasks: links to mockups, wireframes, component designs, and the created `../Conventions/design_conventions.md`. Adherence to [`../Documentation/AI_Design_Agent_Optimization.md`](../Documentation/AI_Design_Agent_Optimization.md:1) is paramount.
   - For backend tasks: API contracts (from `../02_AI-DOCS/Integrations/` or `../03_SPECS/`), data models, and the created `../Conventions/coding_conventions.md`. Adherence to [`../Documentation/AI_Coding_Agent_Optimization.md`](../Documentation/AI_Coding_Agent_Optimization.md:1) is key.
   - For database tasks: schema details, migration scripts, etc.
- Create logical dependencies between tasks.
- Prioritize tasks based on technical dependencies and user value.
- Tag tasks with appropriate categories (UI, Backend, Database, etc.).
- Include acceptance criteria for each task, reflecting the linked specifications.
- Assign an initial `status` (e.g., 'todo', 'in progress', 'done') to each task.
 
### Expected Outputs
 
1. Complete task hierarchy with IDs
2. Complexity analysis report
3. Implementation roadmap with timeline estimates
4. Dependency graph visualization description

Thank you for your assistance in organizing this project for optimal implementation.

## Interactive Workflow with Roo Orchestrator

### Step 1: Project Initialization

You will provide the initial information to Roo Orchestrator as detailed in the "Guiding Roo Orchestrator for Task Management" section. Roo Orchestrator will confirm receipt and understanding of these details.

- `{{project_name}}`: The official name of the project from the PRD
- `{{prd_reference_id}}`: A unique identifier for the PRD document
- `{{project_objective}}`: A concise summary of the project's core purpose from PRD Section 1.2
- `{{features_json}}`: A JSON array of feature objects extracted from PRD Section 3.1

Example features_json format:

```json
[
  {
    "feature_id": "F1",
    "name": "User Authentication",
    "description": "Secure login system with email and social authentication options",
    "key_user_outcomes": "Users can securely access their accounts through multiple authentication methods",
    "priority": "High",
    "complexity": "Medium"
  },
  {
    "feature_id": "F2",
    "name": "Dashboard",
    "description": "Interactive dashboard showing key user metrics and activities",
    "key_user_outcomes": "Users can quickly view and understand their account status and recent activities",
    "priority": "High",
    "complexity": "High"
  }
]
```

### Step 2: Task Decomposition Workflow

Roo Orchestrator will typically prompt you for the next steps or await your instructions. You will guide Roo through the decomposition. Roo Orchestrator may create sub-tasks for Roo Code mode to handle detailed technical aspects or generate related code stubs.

1. **Review Epic Creation**
   - Confirm all features have been properly converted to epics
   - Note the assigned task IDs for each epic

2. **Initiate Detailed Decomposition**
   - For each epic, instruct Roo Orchestrator:
   User to Roo: 'Roo, please perform detailed decomposition of epic {{epic_id}} - "{{epic_name}}". Adhere to these requirements:
   
   Requirements for decomposition:
   - Maximum task size: 4 hours of development work
   - Include technical implementation details
   - Specify UI components, API endpoints, database changes, etc. **in accordance with the project's "Y Combinator" design standards, as detailed in [`../Conventions/design_conventions.md`](../Conventions/design_conventions.md) and [`../Documentation/AI_Design_Agent_Optimization.md`](../Documentation/AI_Design_Agent_Optimization.md).**
   - Create clear acceptance criteria for each task, **including design and UX/UI aspects for relevant tasks, referring to the convention documents.**
   - Establish dependencies between tasks
   - Tag tasks with appropriate categories'
   Roo Orchestrator will process this, potentially involving Roo Code mode for technical details.

3. **Analyze Task Complexity**
   - After task decomposition, instruct Roo Orchestrator:
   User to Roo: 'Roo, please analyze the complexity of all tasks for epic {{epic_id}}.
   
   Provide:
   - Complexity score (1-5) for each task
   - Risk assessment for complex tasks
   - Suggestions for breaking down any tasks still too large
   - Overall complexity assessment for the epic'

4. **Generate Implementation Roadmap**
   - Once all epics are decomposed, instruct Roo Orchestrator:
   User to Roo: 'Roo, please generate a comprehensive implementation roadmap for project {{project_name}}.
   
   Include:
   - Suggested implementation phases
   - Critical path analysis
   - Resource allocation recommendations
   - Timeline estimates with confidence levels
   - Risk mitigation strategies'

### Leveraging Task Hierarchies for High-Quality Agentic Coding

The strategy of breaking down project requirements into a deep hierarchy of tasks (tasks, subtasks, sub-subtasks, etc.) is fundamental to achieving high-quality and reliable outcomes when employing AI coding agents like Roo Code. This granular approach offers several key advantages:

*   **Granularity for Precision:** Decomposing work into very small, specific units provides AI coding agents with a narrow, well-defined scope. This precision minimizes ambiguity and allows the AI to focus on a limited set of requirements, leading to more accurate and targeted code generation.

*   **Clarity of Scope, Details, and Acceptance Criteria:** Each granular unit, regardless of its level in the hierarchy (be it a primary task, subtask, or sub-subtask), must be a complete task object as defined in [`./Tasks_JSON_Structure.md`](./Tasks_JSON_Structure.md:1). This means it must possess a clear `description`, and crucially, comprehensive `details`. **The `details` field is paramount and MUST contain direct links to, or embed content from, all relevant *created* project-specific documents.** This includes:
    *   Design specifications (e.g., from the created `../Conventions/design_conventions.md`, specific mockups, UI component guides).
    *   Coding conventions (from the created `../Conventions/coding_conventions.md`).
    *   Technical specifications (e.g., `../03_SPECS/features/feature_spec_FEAT-XXX.md`, `../02_AI-DOCS/Architecture/architecture.md`, API contracts).
    *   Contextual information inherited from parent tasks.
  The task must also have an explicit `testStrategy`. This `testStrategy`, informed by the linked specifications in `details`, effectively serves as the acceptance criteria for the unit, leaving little room for misinterpretation by the AI.

*   **Enhanced Testability:** Smaller, well-defined units of code are inherently easier to test thoroughly. The specific `testStrategy` associated with each granular task guides both automated testing (e.g., unit tests, integration tests) and manual verification. This focused testing approach helps catch errors early and ensures each component functions as expected.

*   **Iterative Development and Feedback Cycle:** Granularity supports an agile and iterative development process. The AI can complete a small unit of work, which can then be quickly tested and reviewed. Feedback from this review can be incorporated efficiently, and corrections can be made before significant effort is invested in a potentially flawed direction. This rapid feedback loop is crucial for maintaining quality and adapting to any emergent complexities.

*   **Reduced Cognitive Load for AI:** AI agents, much like human developers, perform better when dealing with focused, less complex tasks. By breaking down large problems into manageable sub-units, the cognitive load on the AI is significantly reduced, allowing it to process information more effectively and generate higher-quality code with fewer errors.

*   **Effective Dependency Management:** The clear definition of `dependencies` between these granular tasks (as outlined in [`Tasks_JSON_Structure.md`](./Tasks_JSON_Structure.md:0)) ensures a correct and logical sequence of development. Prerequisites are explicitly stated, preventing out-of-order work and ensuring that foundational components are in place before dependent tasks are attempted.

Roo Orchestrator plays a key role in guiding this detailed decomposition process, ensuring that features from the PRD are meticulously broken down. Subsequently, Roo Code mode benefits immensely from these well-defined, granular tasks, as they provide the precise instructions needed to generate accurate, testable, and high-quality code. This hierarchical and detailed approach to task management is a cornerstone of effective AI-driven (agentic) software development.
### Step 3: Task Refinement

After receiving the complete task breakdown:

1. Review all tasks for clarity and appropriate sizing
2. Identify any tasks that need further decomposition
3. Request refinement of specific tasks if needed:
   User to Roo: 'Roo, please refine task {{task_id}} - "{{task_name}}".
   
   This task appears to be too large/ambiguous. Please break it down further into more precise sub-tasks with clear technical specifications.'

## Best Practices

1. **Maintain Hierarchy**: Ensure a clear 3-level hierarchy (Epics → Tasks → Sub-tasks)
2. **Be Specific**: Each task should have concrete, measurable outcomes
3. **Include Technical Details**: Specify implementation requirements in task descriptions
4. **Set Clear Boundaries**: Define what's in and out of scope for each task
5. **Consider Dependencies**: Establish logical task sequencing
6. **Balance Size**: Tasks should be small enough to be manageable but not so small that overhead increases
7. **Include Validation**: Each task should include acceptance criteria

## Example Task Decomposition

For a feature "User Authentication":

**Epic**: User Authentication System

**Tasks**:
1. Design authentication database schema
2. Implement email registration endpoint
3. Create login form UI component
4. Implement JWT token generation service
5. Create password reset flow

**Sub-tasks** (for "Implement email registration endpoint"):
1. Create API route structure
2. Implement input validation
3. Add email verification logic
4. Implement password hashing
5. Write unit tests for registration endpoint

## Expected Outcome

The result of this process, orchestrated by Roo, will be a comprehensive task breakdown that:

- Provides clear direction for implementation
- Breaks complex features into manageable pieces
- Establishes logical dependencies and sequencing
- Enables accurate progress tracking
- Facilitates efficient resource allocation

This task structure will serve as the foundation for the implementation phase, guiding the development process with precision and clarity.

## Next Steps

### Saving Your Output

Once this task management setup is complete:

1. Roo (either Orchestrator mode directly or via a delegated task to Code mode) will save the task breakdown as [`../tasks/tasks.json`](../tasks/tasks.json:1). The detailed structure for this JSON file is defined in [`./Tasks_JSON_Structure.md`](./Tasks_JSON_Structure.md:1).
2. This task hierarchy will guide the implementation phase.

### Moving to Implementation

To proceed with implementing the project:

1. Open the prompt file in `../01_AI-RUN/` that corresponds to the `07_Start_Building.md` logical step. (Ensure `../01_AI-RUN/01_AutoPilot.md` or your manual process calls the correct actual filename).
2. Share it with your AI agent.
3. Reference your `project_prd.md`, the **created** project-specific documentation in `02_AI-DOCS/` and `../../03_SPECS/`, and the task breakdown in [`../tasks/tasks.json`](../tasks/tasks.json:1).

```
@ImplementationArchitect

Please help me implement my project according to the PRD and task breakdown. You can find:
- The complete PRD at: `project_prd.md`
- The task breakdown at: [`../tasks/tasks.json`](../tasks/tasks.json:1)
- Technical documentation in the `02_AI-DOCS/` (notably [`../Conventions/coding_conventions.md`](../Conventions/coding_conventions.md) and [`../Conventions/design_conventions.md`](../Conventions/design_conventions.md)) and `../../03_SPECS/` directories, as well as the guiding principles from [`../Documentation/AI_Design_Agent_Optimization.md`](../Documentation/AI_Design_Agent_Optimization.md).

I'm ready to start building, beginning with the first task in the hierarchy.
```

### What to Expect Next

In the Implementation phase, the AI will:

1. Set up the development environment according to the technology stack
2. Implement the core architectural components
3. Systematically work through tasks in the hierarchy
4. Write code, tests, and documentation
5. Validate implementations against acceptance criteria

This implementation phase will transform the detailed plans and specifications into a working product, following the roadmap established by the task decomposition. After implementation is complete, the project will move into the "Testing & Preview Visibility" phase to ensure quality before any deployment discussions.


**Output Storage:**

* All task data, managed and generated through Roo's orchestration, will be stored in [`../tasks/tasks.json`](../tasks/tasks.json:1). Each task object in this JSON file should include a `status` attribute (e.g., `"status": "todo"`, `"status": "in progress"`, `"status": "done"`). The detailed structure for this JSON file is defined in [`./Tasks_JSON_Structure.md`](./Tasks_JSON_Structure.md:1).
* This file will be referenced in the implementation phase.

---

## Task Refinement Follow-up

After receiving the initial task breakdown, you may need to further refine complex tasks:

User to Roo: '**Action:** Refine task breakdown for epic [epic_id] and identify subtasks.

**Instructions:**

1. Review the detailed specification for epic [epic_id]
2. Identify complex components that need further breakdown
3. For each complex component, create actionable subtasks
4. Ensure subtasks are granular (4 hours max) but not fragmented
5. Maintain clear parent-child relationships in the task hierarchy

**Expected Response:**
Roo will provide a list of newly created subtasks with their details and confirm their integration into the project's task structure.
*   Confirmation that these subtasks have been added to the project tracking system under the parent epic.