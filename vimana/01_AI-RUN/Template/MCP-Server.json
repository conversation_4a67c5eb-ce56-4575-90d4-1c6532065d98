{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, "performance-analysis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-performance-analysis@2025"]}, "security-analysis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-security-analysis@2025"]}, "a11y": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-a11y@latest"]}, "i18n": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-i18n@latest"]}, "seo": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-seo@latest"]}, "terraform": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-terraform@latest"]}, "jupyter": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-jupyter@latest"]}, "pandas": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-pandas@latest"]}, "sustainability": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sustainability@latest"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "stripe": {"command": "npx", "args": ["-y", "@stripe/mcp", "--tools=all"], "env": {}}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "shadcn": {"command": "npx", "args": ["-y", "shadcn@canary", "registry:mcp"], "env": {"REGISTRY_URL": ""}}, "ElevenLabs": {"command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": ""}}, "convex": {"command": "npx", "args": ["-y", "convex@latest", "mcp", "start"]}, "mcp-server-firecrawl": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": ""}}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "railway": {"command": "npx", "args": ["-y", "@jasontanswe/railway-mcp"], "env": {"RAILWAY_API_TOKEN": ""}}, "@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "", "PERPLEXITY_API_KEY": "", "OPENAI_API_KEY": "", "GOOGLE_API_KEY": "", "MISTRAL_API_KEY": "", "OPENROUTER_API_KEY": "", "XAI_API_KEY": "", "AZURE_OPENAI_API_KEY": ""}}, "notion-adapter": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion-adapter@latest"], "env": {"NOTION_API_KEY": ""}}, "perplexity-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-perplexity-api@latest"], "env": {"PERPLEXITY_API_KEY": ""}}, "google-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-google-search@latest"], "env": {"GOOGLE_API_KEY": "", "GOOGLE_CSE_ID": ""}}, "web-scraper": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-web-scraper@latest"], "env": {}}, "gdpr-compliance": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdpr-compliance@latest"], "env": {}}, "privacy-analysis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-privacy-analysis@latest"], "env": {}}, "monitoring-setup": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-monitoring-setup@latest"], "env": {}}, "chaos-engineering": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-chaos-engineering@latest"], "env": {}}, "openapi-spec": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-openapi-spec@latest"], "env": {}}, "api-linter": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-api-linter@latest"], "env": {}}, "cloud-billing-analysis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-cloud-billing-analysis@latest"], "env": {}}, "license-scanner": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-license-scanner@latest"], "env": {}}, "ip-management": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-ip-management@latest"], "env": {}}, "mlflow": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mlflow@latest"], "env": {}}, "kubeflow": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-kubeflow@latest"], "env": {}}, "sagemaker-pipelines": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sagemaker-pipelines@latest"], "env": {}}, "survey-analytics": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-survey-analytics@latest"], "env": {}}, "sentiment-analysis-text": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sentiment-analysis-text@latest"], "env": {}}, "appstore-reviews": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-appstore-reviews@latest"], "env": {}}, "data-catalog": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-data-catalog@latest"], "env": {}}, "data-lineage": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-data-lineage@latest"], "env": {}}, "policy-management": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-policy-management@latest"], "env": {}}, "confluence-integration": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-confluence-integration@latest"], "env": {}}, "knowledge-graph": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-knowledge-graph@latest"], "env": {}}, "wiki-builder": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-wiki-builder@latest"], "env": {}}, "vector-db": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-vector-db@latest"], "env": {}}, "graph-db": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-graph-db@latest"], "env": {}}, "elasticsearch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-elasticsearch@latest"], "env": {}}, "elasticsearch-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-elasticsearch-api@latest"], "env": {}}, "knowledge-retrieval": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-knowledge-retrieval@latest"], "env": {}}, "service-catalog": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-service-catalog@latest"], "env": {}}, "api-gateway-config": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-api-gateway-config@latest"], "env": {}}, "event-bus-admin": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-event-bus-admin@latest"], "env": {}}, "kubernetes-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-kubernetes-api@latest"], "env": {}}, "helm-cli": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-helm-cli@latest"], "env": {}}, "k8s-config-linter": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-k8s-config-linter@latest"], "env": {}}, "opentelemetry-collector": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-opentelemetry-collector@latest"], "env": {}}, "prometheus-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-prometheus-api@latest"], "env": {}}, "grafana-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-grafana-api@latest"], "env": {}}, "istio-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-istio-api@latest"], "env": {}}, "linkerd-api": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-linkerd-api@latest"], "env": {}}}}